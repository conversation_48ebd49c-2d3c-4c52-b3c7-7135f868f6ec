# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-26 11:52+0000\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: \n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: feed/email_utils.py:202
#, python-format
msgid "Application Status Update - %(job_title)s"
msgstr ""

#: feed/email_utils.py:247
#, python-format
msgid "Meeting Invitation - %(job_title)s"
msgstr ""

#: feed/email_utils.py:329
#, python-format
msgid "Application Received - %(job_title)s"
msgstr ""

#: feed/email_utils.py:376
#, python-format
msgid "Invitation: Join %(company_name)s at Canvider ATS"
msgstr ""

#: feed/views.py:154
#, python-format
msgid "<strong>%(name)s</strong> applied for <strong>%(position)s</strong>"
msgstr ""

#: feed/views.py:195
#, python-format
msgid ""
"<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for "
"<strong>%(position)s</strong>"
msgstr ""

#: feed/views.py:225
#, python-format
msgid "A new vacancy <strong>%(vacancy_title)s</strong> is published"
msgstr ""

#: feed/views.py:249
msgid "New comment on application of"
msgstr ""

#: feed/views.py:251
msgid "New comment on application ID"
msgstr ""

#: feed/views.py:267
msgid "now"
msgstr ""

#: feed/views.py:270
msgid "1 minute ago"
msgstr ""

#: feed/views.py:273
msgid "minutes ago"
msgstr ""

#: feed/views.py:276
msgid "1 hour ago"
msgstr ""

#: feed/views.py:280
msgid "hours ago"
msgstr ""

#: feed/views.py:283
msgid "yesterday"
msgstr ""

#: feed/views.py:287
msgid "days ago"
msgstr ""

#: feed/views.py:290
msgid "last week"
msgstr ""

#: feed/views.py:294
msgid "weeks ago"
msgstr ""

#: feed/views.py:297
msgid "last month"
msgstr ""

#: feed/views.py:301
msgid "months ago"
msgstr ""

#: feed/views.py:306
msgid "last year"
msgstr ""

#: feed/views.py:308
msgid "years ago"
msgstr ""

#: feed/views.py:1045
msgid "Profile photo changed successfully!"
msgstr ""

#: feed/views.py:1050
msgid "Please select a photo."
msgstr ""

#: feed/views.py:1876
#, python-format
msgid "Language changed to %(language)s"
msgstr ""

#: feed/views.py:1880
msgid "Invalid language selection"
msgstr ""

#: feed/views.py:1909 templates/feed.html:17
msgid "Dashboard"
msgstr ""

#: feed/views.py:2079
msgid "Invitation mail sent successfully!"
msgstr ""

#: feed/views.py:2081 feed/views.py:2086 feed/views.py:2229
msgid "Failed to send the invitation. Please check the form."
msgstr ""

#: feed/views.py:2113
msgid "Passwords do not match."
msgstr ""

#: feed/views.py:2144
msgid "No employer found to associate with this account."
msgstr ""

#: feed/views.py:2154
msgid "Registration completed successfully! You can now log in."
msgstr ""

#: feed/views.py:2158
#, python-format
msgid "Error creating account: %(error)s"
msgstr ""

#: feed/views.py:2184
msgid "Access denied."
msgstr ""

#: feed/views.py:2211
msgid "Invitation sent successfully!"
msgstr ""

#: feed/views.py:2240
msgid "User removed successfully!"
msgstr ""

#: feed/views.py:2253
msgid "User status changed successfully!"
msgstr ""

#: feed/views.py:2405
msgid "Talent request sent successfully! Our team will get back to you soon."
msgstr ""

#: feed/views.py:2409
msgid "Invalid request method."
msgstr ""

#: feed/views.py:2462
msgid "Status updated and notification email sent to candidate."
msgstr ""

#: feed/views.py:2464 feed/views.py:2468
msgid "Status updated but email notification failed to send."
msgstr ""

#: feed/views.py:3639
msgid ""
"Mail has been sent successfully, it will appear on this page after a few "
"minutes."
msgstr ""

#: feed/views.py:3641
msgid "Failed to send email. Please try again."
msgstr ""

#: feed/views.py:3646
#, python-format
msgid "Failed to send email: %(error)s"
msgstr ""

#: feed/views.py:4604
msgid "Image URL and employer ID are required"
msgstr ""

#: feed/views.py:4636
#, python-format
msgid "Failed to remove image: %(error)s"
msgstr ""

#: feed/views.py:4641
msgid "Invalid request method"
msgstr ""

#: templates/applicant_dev.html:30
msgid "Applied for:"
msgstr ""

#: templates/applicant_dev.html:89 templates/applicant_dev.html:870
msgid "Schedule Interview"
msgstr ""

#: templates/applicant_dev.html:99
msgid "Change State"
msgstr ""

#: templates/applicant_dev.html:114
msgid "Dashboard & AI"
msgstr ""

#: templates/applicant_dev.html:123
msgid "Candidate Background"
msgstr ""

#: templates/applicant_dev.html:132 templates/applicant_dev.html:565
#: templates/applicant_dev.html:583
msgid "Resume"
msgstr ""

#: templates/applicant_dev.html:141
msgid "Journey"
msgstr ""

#: templates/applicant_dev.html:150 templates/applicant_dev.html:785
msgid "Internal Comments"
msgstr ""

#: templates/applicant_dev.html:159 templates/applicant_dev.html:685
msgid "Emails"
msgstr ""

#: templates/applicant_dev.html:167 templates/applicant_dev.html:854
msgid "Job Details"
msgstr ""

#: templates/applicant_dev.html:191
msgid "Profile Match Analysis"
msgstr ""

#: templates/applicant_dev.html:199
msgid "Key Highlights"
msgstr ""

#: templates/applicant_dev.html:225
msgid "AI analysis will provide candidate highlights."
msgstr ""

#: templates/applicant_dev.html:235
msgid "Areas for Improvement"
msgstr ""

#: templates/applicant_dev.html:272
msgid "Candidate Summary"
msgstr ""

#: templates/applicant_dev.html:321
msgid ""
"Based on the AI analysis, when the resume is compared to the job "
"requirements,"
msgstr ""

#: templates/applicant_dev.html:323
msgid "This candidate is an"
msgstr ""

#: templates/applicant_dev.html:324
msgid "excellent match"
msgstr ""

#: templates/applicant_dev.html:326 templates/applicant_dev.html:329
#: templates/applicant_dev.html:332
msgid "This candidate is a"
msgstr ""

#: templates/applicant_dev.html:327
msgid "good match"
msgstr ""

#: templates/applicant_dev.html:330
msgid "fair match"
msgstr ""

#: templates/applicant_dev.html:333
msgid "weak match"
msgstr ""

#: templates/applicant_dev.html:336
msgid "Analyze the CV with AI to see match details."
msgstr ""

#: templates/applicant_dev.html:363
msgid "AI Analysis Available"
msgstr ""

#: templates/applicant_dev.html:366
msgid "Leverage AI to analyze this candidate's CV against the job description."
msgstr ""

#: templates/applicant_dev.html:387
msgid "Analyze with CanviderAI"
msgstr ""

#: templates/applicant_dev.html:402
msgid "Analyzing CV..."
msgstr ""

#: templates/applicant_dev.html:403
msgid "This may take a moment"
msgstr ""

#: templates/applicant_dev.html:428 templates/applicant_dev.html:449
msgid "Analysis complete"
msgstr ""

#: templates/applicant_dev.html:460
msgid "Candidate Facts"
msgstr ""

#: templates/applicant_dev.html:467
msgid "Applied Position"
msgstr ""

#: templates/applicant_dev.html:473
msgid "Candidate's Address"
msgstr ""

#: templates/applicant_dev.html:476 templates/applicant_dev.html:499
#: templates/applicant_dev.html:506 templates/applicant_dev.html:529
#: templates/applicant_dev.html:536 templates/people.html:119
#: templates/people.html:126 templates/people.html:133
msgid "Not analyzed"
msgstr ""

#: templates/applicant_dev.html:480
#: templates/emails/application_confirmation.html:30
#: templates/emails/application_status_change.html:32
#: templates/emails/general_message.html:32 templates/people.html:61
msgid "Application Date"
msgstr ""

#: templates/applicant_dev.html:486
msgid "Application Portal"
msgstr ""

#: templates/applicant_dev.html:496
msgid "Latest/Current Position"
msgstr ""

#: templates/applicant_dev.html:503
msgid "Latest/Current Employer"
msgstr ""

#: templates/applicant_dev.html:510 templates/jobs.html:69
#: templates/manage_permissions.html:117 templates/manage_permissions.html:264
#: templates/people.html:41 templates/people.html:89
#: templates/published_job_details.html:279
msgid "Status"
msgstr ""

#: templates/applicant_dev.html:516
msgid "Application ID"
msgstr ""

#: templates/applicant_dev.html:526
msgid "Total Experience"
msgstr ""

#: templates/applicant_dev.html:533
msgid "Education Level"
msgstr ""

#: templates/applicant_dev.html:540
msgid "Notice Period"
msgstr ""

#: templates/applicant_dev.html:546
msgid "Last Communication Date"
msgstr ""

#: templates/applicant_dev.html:551 templates/applicant_dev.html:722
msgid "No emails found."
msgstr ""

#: templates/applicant_dev.html:570
msgid "Uploaded on"
msgstr ""

#: templates/applicant_dev.html:588
msgid "Open in New Tab"
msgstr ""

#: templates/applicant_dev.html:593
msgid "Download"
msgstr ""

#: templates/applicant_dev.html:619
msgid "PDF Preview Not Available"
msgstr ""

#: templates/applicant_dev.html:620
msgid ""
"Your browser doesn't support PDF preview. Please download the file to view "
"it."
msgstr ""

#: templates/applicant_dev.html:627
msgid "Download PDF"
msgstr ""

#: templates/applicant_dev.html:644
msgid "Application Stages"
msgstr ""

#: templates/applicant_dev.html:661
msgid "Started on:"
msgstr ""

#: templates/applicant_dev.html:673
msgid "No stages available for this application."
msgstr ""

#: templates/applicant_dev.html:691
msgid "Email History"
msgstr ""

#: templates/applicant_dev.html:701
msgid "From:"
msgstr ""

#: templates/applicant_dev.html:702
msgid "To:"
msgstr ""

#: templates/applicant_dev.html:705 templates/applicant_dev.html:1145
msgid "Subject:"
msgstr ""

#: templates/applicant_dev.html:729 templates/applicant_dev.html:775
msgid "Send Email"
msgstr ""

#: templates/applicant_dev.html:744
msgid "Subject"
msgstr ""

#: templates/applicant_dev.html:754 templates/published_job_details.html:443
msgid "Email Body"
msgstr ""

#: templates/applicant_dev.html:797
msgid "Add a comment"
msgstr ""

#: templates/applicant_dev.html:803
msgid "Add your comment here..."
msgstr ""

#: templates/applicant_dev.html:808
msgid "Post Comment"
msgstr ""

#: templates/applicant_dev.html:841
msgid "No comments yet. Be the first to comment!"
msgstr ""

#: templates/applicant_dev.html:876 templates/feed.html:302
msgid "Event Title"
msgstr ""

#: templates/applicant_dev.html:887 templates/feed.html:312
msgid "Event Type"
msgstr ""

#: templates/applicant_dev.html:893 templates/feed.html:318
msgid "Select an event type"
msgstr ""

#: templates/applicant_dev.html:901 templates/feed.html:326
#: templates/manage_permissions.html:49
msgid "Recruiters"
msgstr ""

#: templates/applicant_dev.html:906 templates/feed.html:331
msgid "Select one or many recruiters"
msgstr ""

#: templates/applicant_dev.html:916
#: templates/emails/application_confirmation.html:26
#: templates/emails/application_status_change.html:20
#: templates/emails/general_message.html:27
#: templates/emails/meeting_invitation.html:31 templates/feed.html:384
#: templates/people.html:31 templates/people.html:88 templates/profile.html:74
#: templates/profile.html:115
msgid "Position"
msgstr ""

#: templates/applicant_dev.html:933 templates/feed.html:406
msgid "Candidate"
msgstr ""

#: templates/applicant_dev.html:949
msgid "Date"
msgstr ""

#: templates/applicant_dev.html:958 templates/feed.html:420
msgid "Start Time"
msgstr ""

#: templates/applicant_dev.html:964 templates/feed.html:425
msgid "End Time"
msgstr ""

#: templates/applicant_dev.html:971 templates/emails/meeting_invitation.html:78
#: templates/feed.html:431
msgid "Meeting Link"
msgstr ""

#: templates/applicant_dev.html:984 templates/feed.html:445
msgid "Generate Mirotalk Link"
msgstr ""

#: templates/applicant_dev.html:999 templates/feed.html:460
msgid "Inform invitees by E-mail"
msgstr ""

#: templates/applicant_dev.html:1004 templates/feed.html:465
msgid "Color"
msgstr ""

#: templates/applicant_dev.html:1006 templates/feed.html:467
msgid "Blue"
msgstr ""

#: templates/applicant_dev.html:1007 templates/feed.html:468
msgid "Light Blue"
msgstr ""

#: templates/applicant_dev.html:1008 templates/feed.html:469
msgid "Purple"
msgstr ""

#: templates/applicant_dev.html:1009 templates/feed.html:470
msgid "Pink"
msgstr ""

#: templates/applicant_dev.html:1016 templates/applicant_dev.html:1173
#: templates/create_job_template.html:205 templates/feed.html:476
#: templates/job_details.html:134 templates/manage_permissions.html:413
#: templates/manage_permissions.html:463 templates/profile.html:125
#: templates/profile.html:152 templates/profile.html:184
#: templates/published_job_details.html:380
#: templates/published_job_details.html:465
#: templates/published_job_details.html:686
msgid "Cancel"
msgstr ""

#: templates/applicant_dev.html:1019 templates/feed.html:479
msgid "Save Event"
msgstr ""

#: templates/applicant_dev.html:1078
msgid "Change Application Status"
msgstr ""

#: templates/applicant_dev.html:1096
msgid "New Status"
msgstr ""

#: templates/applicant_dev.html:1098
#: templates/emails/application_confirmation.html:37
msgid "New"
msgstr ""

#: templates/applicant_dev.html:1099
msgid "Review #1"
msgstr ""

#: templates/applicant_dev.html:1100
msgid "Review #2"
msgstr ""

#: templates/applicant_dev.html:1101
msgid "Review #3"
msgstr ""

#: templates/applicant_dev.html:1102
msgid "Review #4"
msgstr ""

#: templates/applicant_dev.html:1103
msgid "Review #5"
msgstr ""

#: templates/applicant_dev.html:1104
msgid "Ready for Decision"
msgstr ""

#: templates/applicant_dev.html:1105
msgid "Eliminated"
msgstr ""

#: templates/applicant_dev.html:1106
msgid "Offer Made"
msgstr ""

#: templates/applicant_dev.html:1107
msgid "Candidate Accepted"
msgstr ""

#: templates/applicant_dev.html:1108
msgid "Candidate Rejected"
msgstr ""

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "Internal Notes"
msgstr ""

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "(visible only to recruiters)"
msgstr ""

#: templates/applicant_dev.html:1126
msgid "Notify candidate about this status change via email"
msgstr ""

#: templates/applicant_dev.html:1132
msgid "Email Message"
msgstr ""

#: templates/applicant_dev.html:1132
msgid "(will be included in the email to the candidate)"
msgstr ""

#: templates/applicant_dev.html:1143
msgid "Email Preview"
msgstr ""

#: templates/applicant_dev.html:1145
msgid "Your application status has been updated"
msgstr ""

#: templates/applicant_dev.html:1175
msgid "Save Change"
msgstr ""

#: templates/careers_page.html:10
msgid "Career Page Setup"
msgstr ""

#: templates/careers_page.html:11
msgid "Choose the integration method that best suits your needs"
msgstr ""

#: templates/careers_page.html:23
msgid "RSS Feed Integration"
msgstr ""

#: templates/careers_page.html:24
msgid ""
"Already have your own careers page? Get our RSS feed to sync job listings"
msgstr ""

#: templates/careers_page.html:26
msgid "Quick Setup"
msgstr ""

#: templates/careers_page.html:27
msgid "Auto Sync"
msgstr ""

#: templates/careers_page.html:32
msgid "Get RSS Feed"
msgstr ""

#: templates/careers_page.html:40
msgid "Recommended"
msgstr ""

#: templates/careers_page.html:45
msgid "Full HTML Page"
msgstr ""

#: templates/careers_page.html:46
msgid "Let us manage your entire careers page with our professional template"
msgstr ""

#: templates/careers_page.html:48
msgid "Professional"
msgstr ""

#: templates/careers_page.html:49
msgid "Customizable"
msgstr ""

#: templates/careers_page.html:54
msgid "Create Page"
msgstr ""

#: templates/careers_page.html:67
msgid "Workloupe Platform"
msgstr ""

#: templates/careers_page.html:68
msgid "Use our platform as your company's career page"
msgstr ""

#: templates/careers_page.html:70
msgid "Hosted"
msgstr ""

#: templates/careers_page.html:71
msgid "Full Featured"
msgstr ""

#: templates/careers_page.html:76
msgid "Setup Platform"
msgstr ""

#: templates/careers_page.html:91
msgid "Your RSS Feed URL"
msgstr ""

#: templates/careers_page.html:98
msgid ""
"Use this RSS feed URL to automatically sync your job listings with your "
"existing careers page."
msgstr ""

#: templates/careers_page.html:101
msgid "RSS Feed URL"
msgstr ""

#: templates/careers_page.html:106 templates/published_job_details.html:516
#: templates/published_job_details.html:559
#: templates/published_job_details.html:585
#: templates/workloupe_platform.html:311
msgid "Copy"
msgstr ""

#: templates/careers_page.html:111 templates/create_careers_widget.html:182
msgid "Integration Instructions:"
msgstr ""

#: templates/careers_page.html:113
msgid "Copy the RSS feed URL above"
msgstr ""

#: templates/careers_page.html:114
msgid "Add it to your website's RSS feed reader or job board integration"
msgstr ""

#: templates/careers_page.html:115
msgid "Your job listings will automatically sync"
msgstr ""

#: templates/careers_page.html:286 templates/create_careers_widget.html:716
#: templates/wordpress_integration.html:775
#: templates/workloupe_platform.html:1142
msgid "Copied!"
msgstr ""

#: templates/careers_page.html:297 templates/create_careers_widget.html:726
#: templates/wordpress_integration.html:785
msgid "Failed to copy. Please copy manually."
msgstr ""

#: templates/create_careers_widget.html:13
msgid "Widget Builder"
msgstr ""

#: templates/create_careers_widget.html:14
msgid "Customize your careers widget"
msgstr ""

#: templates/create_careers_widget.html:22
#: templates/wordpress_integration.html:45
msgid "Company Branding"
msgstr ""

#: templates/create_careers_widget.html:26
#: templates/wordpress_integration.html:49 templates/workloupe_platform.html:34
msgid "Company Name"
msgstr ""

#: templates/create_careers_widget.html:27
#: templates/wordpress_integration.html:50
msgid "Enter company name"
msgstr ""

#: templates/create_careers_widget.html:31
#: templates/wordpress_integration.html:54
msgid "Tagline"
msgstr ""

#: templates/create_careers_widget.html:32
#: templates/wordpress_integration.html:55
msgid "Enter company tagline"
msgstr ""

#: templates/create_careers_widget.html:36
#: templates/workloupe_platform.html:175
msgid "Company Logo"
msgstr ""

#: templates/create_careers_widget.html:38
msgid "Recommended: 200x80px, PNG or JPG"
msgstr ""

#: templates/create_careers_widget.html:46
msgid "Design & Colors"
msgstr ""

#: templates/create_careers_widget.html:50
#: templates/wordpress_integration.html:67
msgid "Primary Color"
msgstr ""

#: templates/create_careers_widget.html:55
msgid "Background Color"
msgstr ""

#: templates/create_careers_widget.html:60
msgid "Text Color"
msgstr ""

#: templates/create_careers_widget.html:65
msgid "Widget Style"
msgstr ""

#: templates/create_careers_widget.html:67
#: templates/wordpress_integration.html:75
msgid "Modern"
msgstr ""

#: templates/create_careers_widget.html:68
#: templates/wordpress_integration.html:76
msgid "Classic"
msgstr ""

#: templates/create_careers_widget.html:69
#: templates/wordpress_integration.html:77
msgid "Minimal"
msgstr ""

#: templates/create_careers_widget.html:78
#: templates/wordpress_integration.html:93
msgid "Content Settings"
msgstr ""

#: templates/create_careers_widget.html:82
msgid "Max Jobs to Display"
msgstr ""

#: templates/create_careers_widget.html:84
#: templates/create_careers_widget.html:85
#: templates/create_careers_widget.html:86 templates/jobs.html:186
#: templates/wordpress_integration.html:99
#: templates/wordpress_integration.html:100
#: templates/wordpress_integration.html:101
msgid "jobs"
msgstr ""

#: templates/create_careers_widget.html:87
#: templates/wordpress_integration.html:102
msgid "All jobs"
msgstr ""

#: templates/create_careers_widget.html:94
msgid "Show Salary Information"
msgstr ""

#: templates/create_careers_widget.html:101
msgid "Show Job Location"
msgstr ""

#: templates/create_careers_widget.html:108
msgid "Show Posted Date"
msgstr ""

#: templates/create_careers_widget.html:117
msgid "Generate Widget Code"
msgstr ""

#: templates/create_careers_widget.html:126
msgid "Live Preview"
msgstr ""

#: templates/create_careers_widget.html:160
msgid "Your Widget Code"
msgstr ""

#: templates/create_careers_widget.html:167
msgid ""
"Copy this code and paste it into your website where you want the careers "
"widget to appear."
msgstr ""

#: templates/create_careers_widget.html:172
msgid "HTML Widget Code"
msgstr ""

#: templates/create_careers_widget.html:175
#: templates/wordpress_integration.html:246
msgid "Copy Code"
msgstr ""

#: templates/create_careers_widget.html:184
msgid "Copy the HTML code above"
msgstr ""

#: templates/create_careers_widget.html:185
msgid "Paste it into your website's HTML where you want the widget to appear"
msgstr ""

#: templates/create_careers_widget.html:186
msgid "The widget will automatically load your latest job postings"
msgstr ""

#: templates/create_careers_widget.html:187
msgid "The widget is responsive and will adapt to your website's layout"
msgstr ""

#: templates/create_careers_widget.html:192
#: templates/wordpress_integration.html:253
#: templates/workloupe_platform.html:338
msgid "Close"
msgstr ""

#: templates/create_careers_widget.html:195
msgid "Download as HTML File"
msgstr ""

#: templates/create_careers_widget.html:802
msgid ""
"Widget package downloaded! Extract and follow the README instructions for "
"integration."
msgstr ""

#: templates/create_careers_widget.html:823
msgid "Widget file downloaded! Copy the code and paste it into your website."
msgstr ""

#: templates/create_job.html:3
msgid "Create Job Position"
msgstr ""

#: templates/create_job.html:8
msgid "Basic Information"
msgstr ""

#: templates/create_job.html:10 templates/job_details.html:1072
#: templates/job_preview_publish.html:590
msgid "Role Title"
msgstr ""

#: templates/create_job.html:14
msgid "e.g. Senior Software Engineer"
msgstr ""

#: templates/create_job.html:18 templates/job_details.html:1076
#: templates/job_preview_publish.html:594
msgid "Office Location"
msgstr ""

#: templates/create_job.html:21
msgid "Select office location"
msgstr ""

#: templates/create_job.html:31
msgid "No office locations found. Please"
msgstr ""

#: templates/create_job.html:32
msgid "add office locations"
msgstr ""

#: templates/create_job.html:32 templates/create_job.html:53
#: templates/create_job.html:74 templates/create_job.html:95
msgid "in your preferences first."
msgstr ""

#: templates/create_job.html:36
msgid "No locations available"
msgstr ""

#: templates/create_job.html:41 templates/job_details.html:1080
#: templates/job_preview_publish.html:598
msgid "Work Schedule"
msgstr ""

#: templates/create_job.html:44 templates/create_job.html:65
msgid "Select an option"
msgstr ""

#: templates/create_job.html:52
msgid "No work schedules found. Please"
msgstr ""

#: templates/create_job.html:53
msgid "add work schedules"
msgstr ""

#: templates/create_job.html:57
msgid "No work schedules available"
msgstr ""

#: templates/create_job.html:62 templates/job_details.html:1084
#: templates/job_preview_publish.html:604
msgid "Office Schedule"
msgstr ""

#: templates/create_job.html:73
msgid "No office schedules found. Please"
msgstr ""

#: templates/create_job.html:74
msgid "add office schedules"
msgstr ""

#: templates/create_job.html:78
msgid "No office schedules available"
msgstr ""

#: templates/create_job.html:83 templates/job_details.html:1088
#: templates/job_preview_publish.html:610 templates/jobs.html:59
#: templates/profile.html:119
msgid "Department"
msgstr ""

#: templates/create_job.html:86
msgid "Select a department"
msgstr ""

#: templates/create_job.html:94
msgid "No departments found. Please"
msgstr ""

#: templates/create_job.html:95
msgid "add departments"
msgstr ""

#: templates/create_job.html:99
msgid "No departments available"
msgstr ""

#: templates/create_job.html:107
msgid "Skills Requirements"
msgstr ""

#: templates/create_job.html:109
msgid "Skill"
msgstr ""

#: templates/create_job.html:114
msgid "e.g. JavaScript"
msgstr ""

#: templates/create_job.html:116 templates/create_job.html:190
msgid "Add"
msgstr ""

#: templates/create_job.html:119
msgid "Choose Skills"
msgstr ""

#: templates/create_job.html:133
msgid "Selected Skills"
msgstr ""

#: templates/create_job.html:136
msgid "No skills selected yet"
msgstr ""

#: templates/create_job.html:146
msgid "Salary Details (Optional)"
msgstr ""

#: templates/create_job.html:149
msgid "Minimum Salary"
msgstr ""

#: templates/create_job.html:153
msgid "Enter minimum salary"
msgstr ""

#: templates/create_job.html:157
msgid "Maximum Salary"
msgstr ""

#: templates/create_job.html:161
msgid "Enter maximum salary"
msgstr ""

#: templates/create_job.html:165
msgid "Currency"
msgstr ""

#: templates/create_job.html:167
msgid "Select currency"
msgstr ""

#: templates/create_job.html:183
msgid "Benefits and Highlights (Optional)"
msgstr ""

#: templates/create_job.html:188
msgid "e.g. Yearly Bonuses"
msgstr ""

#: templates/create_job.html:193
msgid "Choose Benefits"
msgstr ""

#: templates/create_job.html:196
msgid "Dental Coverage"
msgstr ""

#: templates/create_job.html:199
msgid "Private Health Coverage"
msgstr ""

#: templates/create_job.html:202
msgid "Gym membership"
msgstr ""

#: templates/create_job.html:205
msgid "Sign-in Bonus"
msgstr ""

#: templates/create_job.html:208
msgid "Relocation Package"
msgstr ""

#: templates/create_job.html:211
msgid "Company Vehicle"
msgstr ""

#: templates/create_job.html:213
msgid "Food Card"
msgstr ""

#: templates/create_job.html:215
msgid "Snacks & Coffee"
msgstr ""

#: templates/create_job.html:218
msgid "Pet Friendly Office"
msgstr ""

#: templates/create_job.html:222
msgid "Selected Benefits & Highlights"
msgstr ""

#: templates/create_job.html:225
msgid "No benefits or highlights selected yet"
msgstr ""

#: templates/create_job.html:232 templates/job_details.html:107
#: templates/job_preview_publish.html:159
msgid "Discard"
msgstr ""

#: templates/create_job.html:233
msgid "Next"
msgstr ""

#: templates/create_job_template.html:13 templates/create_job_template.html:19
#: templates/settings.html:41
msgid "Templates"
msgstr ""

#: templates/create_job_template.html:14
msgid "Create and manage reusable job description templates"
msgstr ""

#: templates/create_job_template.html:17 templates/job_preferences.html:15
#: templates/manage_permissions.html:15 templates/navbar.html:104
#: templates/settings.html:9
msgid "Settings"
msgstr ""

#: templates/create_job_template.html:31
msgid "Total Templates"
msgstr ""

#: templates/create_job_template.html:41
msgid "Created This Month"
msgstr ""

#: templates/create_job_template.html:51
msgid "Jobs Created from Templates"
msgstr ""

#: templates/create_job_template.html:61
msgid "Time Saved Using Templates"
msgstr ""

#: templates/create_job_template.html:71
msgid "My Templates"
msgstr ""

#: templates/create_job_template.html:74 templates/create_job_template.html:105
#: templates/create_job_template.html:118
msgid "New Template"
msgstr ""

#: templates/create_job_template.html:80
msgid "Search templates..."
msgstr ""

#: templates/create_job_template.html:91
msgid "Updated"
msgstr ""

#: templates/create_job_template.html:94
msgid "Used 1 time"
msgstr ""

#: templates/create_job_template.html:96
msgid "Used"
msgstr ""

#: templates/create_job_template.html:96
msgid "times"
msgstr ""

#: templates/create_job_template.html:107
msgid "Not saved yet"
msgstr ""

#: templates/create_job_template.html:108
msgid "Not used yet"
msgstr ""

#: templates/create_job_template.html:118
msgid "Enter template title"
msgstr ""

#: templates/create_job_template.html:121
#: templates/create_job_template.html:196
msgid "Delete Template"
msgstr ""

#: templates/create_job_template.html:126 templates/job_details.html:135
msgid "Save Template"
msgstr ""

#: templates/create_job_template.html:136
msgid "Heading 1"
msgstr ""

#: templates/create_job_template.html:137
msgid "Heading 2"
msgstr ""

#: templates/create_job_template.html:138
msgid "Heading 3"
msgstr ""

#: templates/create_job_template.html:139
msgid "Paragraph"
msgstr ""

#: templates/create_job_template.html:143
msgid "Bold"
msgstr ""

#: templates/create_job_template.html:144
msgid "Italic"
msgstr ""

#: templates/create_job_template.html:145
msgid "Underline"
msgstr ""

#: templates/create_job_template.html:149
msgid "Bullet List"
msgstr ""

#: templates/create_job_template.html:150
msgid "Numbered List"
msgstr ""

#: templates/create_job_template.html:179
msgid "Enter your template content here..."
msgstr ""

#: templates/create_job_template.html:185 templates/job_details.html:100
msgid "characters"
msgstr ""

#: templates/create_job_template.html:201
msgid "Are you sure you want to delete the"
msgstr ""

#: templates/create_job_template.html:201
msgid "template? This action cannot be undone."
msgstr ""

#: templates/create_job_template.html:206 templates/job_preferences.html:70
msgid "Delete"
msgstr ""

#: templates/emails/application_confirmation.html:4
msgid "Application Received"
msgstr ""

#: templates/emails/application_confirmation.html:6
msgid "Application Confirmation"
msgstr ""

#: templates/emails/application_confirmation.html:11
msgid "Thank you for your application!"
msgstr ""

#: templates/emails/application_confirmation.html:14
#, python-format
msgid ""
"\n"
"        We have successfully received your application for the position of "
"<strong>%(job_title)s</strong>.\n"
"        "
msgstr ""

#: templates/emails/application_confirmation.html:22
msgid "Application Summary"
msgstr ""

#: templates/emails/application_confirmation.html:34
#: templates/emails/application_status_change.html:24
msgid "Application Status"
msgstr ""

#: templates/emails/application_confirmation.html:43
msgid "Application Source"
msgstr ""

#: templates/emails/application_confirmation.html:51
msgid "What Happens Next?"
msgstr ""

#: templates/emails/application_confirmation.html:53
msgid "Our recruitment team will review your application"
msgstr ""

#: templates/emails/application_confirmation.html:54
msgid ""
"If your profile matches our requirements, we will contact you for the next "
"steps"
msgstr ""

#: templates/emails/application_confirmation.html:55
msgid ""
"You will receive email updates about any changes to your application status"
msgstr ""

#: templates/emails/application_confirmation.html:56
msgid "The review process typically takes 5-10 business days"
msgstr ""

#: templates/emails/application_confirmation.html:61
msgid "Stay Updated"
msgstr ""

#: templates/emails/application_confirmation.html:63
msgid ""
"We will keep you informed throughout the selection process via email. Please "
"ensure that emails from our domain are not filtered as spam."
msgstr ""

#: templates/emails/application_confirmation.html:66
msgid ""
"If you have any questions about your application or the position, please "
"don't hesitate to contact us using the reference ID provided at the bottom "
"of this email."
msgstr ""

#: templates/emails/application_confirmation.html:71
msgid "About Our Company"
msgstr ""

#: templates/emails/application_confirmation.html:73
msgid ""
"We appreciate your interest in joining our team. We are committed to "
"creating an inclusive and diverse workplace where everyone can thrive."
msgstr ""

#: templates/emails/application_confirmation.html:79
msgid ""
"Thank you for considering us as your next career opportunity. We look "
"forward to potentially working with you!"
msgstr ""

#: templates/emails/application_status_change.html:4
#: templates/emails/application_status_change.html:6
msgid "Application Status Update"
msgstr ""

#: templates/emails/application_status_change.html:11
#, python-format
msgid ""
"\n"
"        We would like to inform you that your application for the position "
"of <strong>%(job_title)s</strong> has been updated.\n"
"        "
msgstr ""

#: templates/emails/application_status_change.html:37
msgid "Last Updated"
msgstr ""

#: templates/emails/application_status_change.html:46
msgid "Additional Information"
msgstr ""

#: templates/emails/application_status_change.html:54
msgid "Congratulations! We are excited to welcome you to our team."
msgstr ""

#: templates/emails/application_status_change.html:57
msgid ""
"Our HR team will be in touch with you shortly regarding the next steps, "
"including onboarding information and your start date."
msgstr ""

#: templates/emails/application_status_change.html:63
msgid ""
"Your application is currently under review. We will keep you updated on any "
"developments."
msgstr ""

#: templates/emails/application_status_change.html:67
msgid "Your application has reached the final stage of our selection process."
msgstr ""

#: templates/emails/application_status_change.html:74
msgid ""
"While we were impressed with your qualifications, we have decided to move "
"forward with other candidates for this particular position."
msgstr ""

#: templates/emails/application_status_change.html:77
msgid ""
"We encourage you to apply for future opportunities that match your skills "
"and experience. We will keep your profile in our talent database for "
"consideration in upcoming roles."
msgstr ""

#: templates/emails/application_status_change.html:83
msgid ""
"We will continue to review your application and keep you informed of any "
"updates throughout the process."
msgstr ""

#: templates/emails/application_status_change.html:90
msgid ""
"Thank you for your interest in joining our team. We appreciate the time you "
"have invested in the application process."
msgstr ""

#: templates/emails/base_email.html:219 templates/emails/base_email.html:236
msgid "Recruitment Team"
msgstr ""

#: templates/emails/base_email.html:226 templates/emails/team_invitation.html:9
msgid "Dear"
msgstr ""

#: templates/emails/base_email.html:237
msgid "Human Resources Department"
msgstr ""

#: templates/emails/base_email.html:244
msgid "If you have any questions, please don't hesitate to contact us."
msgstr ""

#: templates/emails/base_email.html:248
msgid "Reference ID:"
msgstr ""

#: templates/emails/base_email.html:252
msgid "This email was sent by"
msgstr ""

#: templates/emails/general_message.html:6
msgid "Message from Recruitment Team"
msgstr ""

#: templates/emails/general_message.html:12
#, python-format
msgid ""
"\n"
"        This message is regarding your application for the position of "
"<strong>%(job_title)s</strong>.\n"
"        "
msgstr ""

#: templates/emails/general_message.html:36
msgid "Current Status"
msgstr ""

#: templates/emails/general_message.html:50
msgid ""
"Thank you for your continued interest in our company. We appreciate your "
"patience throughout the application process."
msgstr ""

#: templates/emails/meeting_invitation.html:4
msgid "Meeting Invitation"
msgstr ""

#: templates/emails/meeting_invitation.html:6
msgid "Interview Invitation"
msgstr ""

#: templates/emails/meeting_invitation.html:11
msgid "Great news! We would like to invite you for an interview."
msgstr ""

#: templates/emails/meeting_invitation.html:14
#, python-format
msgid ""
"\n"
"        We are pleased to invite you to an interview for the position of "
"<strong>%(job_title)s</strong>. \n"
"        We were impressed with your application and would like to learn more "
"about you.\n"
"        "
msgstr ""

#: templates/emails/meeting_invitation.html:23
msgid "Meeting Details"
msgstr ""

#: templates/emails/meeting_invitation.html:27
msgid "Meeting Title"
msgstr ""

#: templates/emails/meeting_invitation.html:35
msgid "Interview Type"
msgstr ""

#: templates/emails/meeting_invitation.html:38
msgid "Video Call"
msgstr ""

#: templates/emails/meeting_invitation.html:40
msgid "Phone Call"
msgstr ""

#: templates/emails/meeting_invitation.html:42
msgid "In-Person Meeting"
msgstr ""

#: templates/emails/meeting_invitation.html:49
msgid "Date & Time"
msgstr ""

#: templates/emails/meeting_invitation.html:59
msgid "Duration"
msgstr ""

#: templates/emails/meeting_invitation.html:68
msgid "Interviewer(s)"
msgstr ""

#: templates/emails/meeting_invitation.html:81
msgid "Join Meeting"
msgstr ""

#: templates/emails/meeting_invitation.html:92
msgid "Video Call Instructions"
msgstr ""

#: templates/emails/meeting_invitation.html:94
msgid "Please test your camera and microphone before the meeting"
msgstr ""

#: templates/emails/meeting_invitation.html:95
msgid "Ensure you have a stable internet connection"
msgstr ""

#: templates/emails/meeting_invitation.html:96
msgid "Find a quiet, well-lit space for the interview"
msgstr ""

#: templates/emails/meeting_invitation.html:97
msgid "Click the meeting link 5 minutes before the scheduled time"
msgstr ""

#: templates/emails/meeting_invitation.html:102
msgid "Phone Call Instructions"
msgstr ""

#: templates/emails/meeting_invitation.html:104
msgid "Please ensure you're in a quiet location"
msgstr ""

#: templates/emails/meeting_invitation.html:105
msgid "Have a good phone signal or use a landline if possible"
msgstr ""

#: templates/emails/meeting_invitation.html:106
msgid "We will call you at the scheduled time"
msgstr ""

#: templates/emails/meeting_invitation.html:107
msgid "Have your resume and any questions ready"
msgstr ""

#: templates/emails/meeting_invitation.html:112
msgid "In-Person Meeting Instructions"
msgstr ""

#: templates/emails/meeting_invitation.html:114
msgid "Please arrive 10 minutes early"
msgstr ""

#: templates/emails/meeting_invitation.html:115
msgid "Bring a printed copy of your resume"
msgstr ""

#: templates/emails/meeting_invitation.html:116
msgid "Dress professionally"
msgstr ""

#: templates/emails/meeting_invitation.html:117
msgid "Ask for directions at the reception if needed"
msgstr ""

#: templates/emails/meeting_invitation.html:123
msgid "What to Expect"
msgstr ""

#: templates/emails/meeting_invitation.html:125
msgid ""
"During the interview, we will discuss your background, experience, and how "
"you can contribute to our team. This is also a great opportunity for you to "
"ask questions about the role and our company culture."
msgstr ""

#: templates/emails/meeting_invitation.html:130
msgid "Need to Reschedule?"
msgstr ""

#: templates/emails/meeting_invitation.html:132
msgid ""
"If you need to reschedule this interview, please contact us as soon as "
"possible. When reaching out, please include the reference ID mentioned at "
"the bottom of this email."
msgstr ""

#: templates/emails/meeting_invitation.html:138
msgid ""
"We look forward to meeting with you and learning more about your "
"qualifications!"
msgstr ""

#: templates/emails/team_invitation.html:4
msgid "Team Invitation"
msgstr ""

#: templates/emails/team_invitation.html:6
msgid "Join Our Team"
msgstr ""

#: templates/emails/team_invitation.html:15
msgid "You've been invited to join our team!"
msgstr ""

#: templates/emails/team_invitation.html:18
#, python-format
msgid ""
"\n"
"        You have been invited to join the <strong>%(company_name)s</strong> "
"team at Canvider ATS as a <strong>%(role)s</strong>.\n"
"        "
msgstr ""

#: templates/emails/team_invitation.html:26
msgid "Invitation Details"
msgstr ""

#: templates/emails/team_invitation.html:30 templates/profile.html:66
#: templates/profile.html:111
msgid "Company"
msgstr ""

#: templates/emails/team_invitation.html:34
#: templates/manage_permissions.html:113 templates/manage_permissions.html:252
#: templates/manage_permissions.html:381
msgid "Role"
msgstr ""

#: templates/emails/team_invitation.html:38
msgid "Invitation Expires"
msgstr ""

#: templates/emails/team_invitation.html:46 templates/register.html:11
msgid "Accept Invitation"
msgstr ""

#: templates/emails/team_invitation.html:51
msgid "Can't click the button?"
msgstr ""

#: templates/emails/team_invitation.html:53
msgid "Copy and paste this link into your browser:"
msgstr ""

#: templates/emails/team_invitation.html:61
msgid "Important Information"
msgstr ""

#: templates/emails/team_invitation.html:63
msgid "This invitation is valid until"
msgstr ""

#: templates/emails/team_invitation.html:64
msgid "You will need to create a password when accepting the invitation"
msgstr ""

#: templates/emails/team_invitation.html:65
msgid "If you have any questions, please contact the administrator"
msgstr ""

#: templates/emails/team_invitation.html:71
msgid "We look forward to having you on our team!"
msgstr ""

#: templates/feed.html:20 templates/feed.html:46
msgid "Loading..."
msgstr ""

#: templates/feed.html:32
msgid "Calendar"
msgstr ""

#: templates/feed.html:34
msgid "Day"
msgstr ""

#: templates/feed.html:35
msgid "Week"
msgstr ""

#: templates/feed.html:36
msgid "Month"
msgstr ""

#: templates/feed.html:51 templates/jobs.html:92 templates/people.html:64
msgid "Today"
msgstr ""

#: templates/feed.html:56
msgid "Click on a day with colored dots to view events"
msgstr ""

#: templates/feed.html:61 templates/feed.html:74
msgid "Mon"
msgstr ""

#: templates/feed.html:62 templates/feed.html:75
msgid "Tue"
msgstr ""

#: templates/feed.html:63 templates/feed.html:76
msgid "Wed"
msgstr ""

#: templates/feed.html:64 templates/feed.html:77
msgid "Thu"
msgstr ""

#: templates/feed.html:65 templates/feed.html:78
msgid "Fri"
msgstr ""

#: templates/feed.html:66 templates/feed.html:79
msgid "Sat"
msgstr ""

#: templates/feed.html:67 templates/feed.html:80
msgid "Sun"
msgstr ""

#: templates/feed.html:96
msgid "Activity Feed"
msgstr ""

#: templates/feed.html:100
msgid "Clear"
msgstr ""

#: templates/feed.html:147 templates/feed.html:3030
msgid "No Recent Activity"
msgstr ""

#: templates/feed.html:149 templates/feed.html:3032
msgid ""
"Activity will appear here when candidates apply, change status, or when you "
"post new jobs."
msgstr ""

#: templates/feed.html:166
msgid "Hot"
msgstr ""

#: templates/feed.html:166 templates/navbar.html:36
msgid "Jobs"
msgstr ""

#: templates/feed.html:169
msgid "View All Jobs"
msgstr ""

#: templates/feed.html:186 templates/feed.html:245 templates/jobs.html:129
#: templates/navbar.html:42
msgid "Applicants"
msgstr ""

#: templates/feed.html:203
msgid "No Hot Jobs Yet"
msgstr ""

#: templates/feed.html:205
msgid ""
"Create your first job posting to start attracting candidates and see "
"trending positions here."
msgstr ""

#: templates/feed.html:209 templates/navbar.html:57
msgid "Create Job"
msgstr ""

#: templates/feed.html:212 templates/jobs.html:175
msgid "Browse Templates"
msgstr ""

#: templates/feed.html:228
msgid "Monthly Applicant Overview"
msgstr ""

#: templates/feed.html:267
msgid "Events for Date"
msgstr ""

#: templates/feed.html:289
msgid "Add New Event"
msgstr ""

#: templates/feed.html:299
msgid "Create New Event"
msgstr ""

#: templates/feed.html:306
msgid "Enter event title"
msgstr ""

#: templates/feed.html:393
msgid "Select the relevant position"
msgstr ""

#: templates/feed.html:400
msgid "No vacancies available"
msgstr ""

#: templates/feed.html:413
msgid "Pick a Vacancy to see candidates"
msgstr ""

#: templates/feed.html:436
msgid "Enter meeting link"
msgstr ""

#: templates/feed.html:2975
msgid ""
"Are you sure you want to clear all activity notifications? This action "
"cannot be undone."
msgstr ""

#: templates/feed.html:3046
msgid "Activity feed cleared successfully"
msgstr ""

#: templates/feed.html:3048
msgid "Failed to clear activity feed"
msgstr ""

#: templates/feed.html:3053
msgid "An error occurred while clearing activity feed"
msgstr ""

#: templates/job_details.html:7 templates/job_details.html:20
#: templates/job_details.html:51 templates/job_preview_publish.html:28
msgid "Job Description"
msgstr ""

#: templates/job_details.html:11 templates/job_preview_publish.html:17
msgid "Job Summary"
msgstr ""

#: templates/job_details.html:14 templates/job_preview_publish.html:21
msgid "Loading job details..."
msgstr ""

#: templates/job_details.html:24
msgid "Create new description"
msgstr ""

#: templates/job_details.html:28
msgid "Use saved template"
msgstr ""

#: templates/job_details.html:33
msgid "Choose a template:"
msgstr ""

#: templates/job_details.html:35
msgid "Select a template"
msgstr ""

#: templates/job_details.html:42
msgid "AI Job Description Generator"
msgstr ""

#: templates/job_details.html:43
msgid ""
"Let AI create a professional job description based on your job details above."
msgstr ""

#: templates/job_details.html:46
msgid "Generate with AI"
msgstr ""

#: templates/job_details.html:52
msgid ""
"Describe the position, responsibilities, qualifications, and any other "
"relevant details."
msgstr ""

#: templates/job_details.html:106 templates/job_preview_publish.html:158
msgid "Back"
msgstr ""

#: templates/job_details.html:111
msgid "Update Template"
msgstr ""

#: templates/job_details.html:114 templates/job_details.html:124
msgid "Save as Template"
msgstr ""

#: templates/job_details.html:117
msgid "Save & Continue"
msgstr ""

#: templates/job_details.html:129
msgid "Template Title"
msgstr ""

#: templates/job_details.html:130
msgid "Enter a name for this template"
msgstr ""

#: templates/job_details.html:1060
msgid ""
"No job information found. Please go back and fill out the job details form."
msgstr ""

#: templates/job_details.html:1093 templates/job_preview_publish.html:614
msgid "Salary Details"
msgstr ""

#: templates/job_details.html:1098 templates/job_preview_publish.html:621
msgid "Benefits & Highlights"
msgstr ""

#: templates/job_details.html:1108 templates/job_preview_publish.html:637
msgid "Skills"
msgstr ""

#: templates/job_preferences.html:11 templates/job_preferences.html:17
#: templates/settings.html:20
msgid "Preferences"
msgstr ""

#: templates/job_preferences.html:12
msgid "Configure standard options to streamline your job creation process"
msgstr ""

#: templates/job_preferences.html:26 templates/job_preferences.html:51
msgid "Work Schedules"
msgstr ""

#: templates/job_preferences.html:30 templates/job_preferences.html:84
msgid "Office Schedules"
msgstr ""

#: templates/job_preferences.html:34
msgid "Locations"
msgstr ""

#: templates/job_preferences.html:38
msgid "Departments"
msgstr ""

#: templates/job_preferences.html:42
msgid "Language"
msgstr ""

#: templates/job_preferences.html:52
msgid "Define standard work schedule types for your organization"
msgstr ""

#: templates/job_preferences.html:56
msgid "Add Work Schedule"
msgstr ""

#: templates/job_preferences.html:64
msgid "Search work schedules..."
msgstr ""

#: templates/job_preferences.html:67
msgid "Select All"
msgstr ""

#: templates/job_preferences.html:85
msgid "Define where and how employees work"
msgstr ""

#: templates/job_preferences.html:89
msgid "Add Office Schedule"
msgstr ""

#: templates/job_preferences.html:183
msgid "Language Settings"
msgstr ""

#: templates/job_preferences.html:184
msgid "Choose your preferred language for the application interface"
msgstr ""

#: templates/job_preferences.html:189
msgid "Interface Language"
msgstr ""

#: templates/job_preferences.html:190
msgid "Select the language you want to use for the application interface"
msgstr ""

#: templates/job_preferences.html:210
msgid "Current"
msgstr ""

#: templates/job_preferences.html:223 templates/published_job_details.html:652
#: templates/workloupe_platform.html:213
msgid "Note:"
msgstr ""

#: templates/job_preferences.html:223
msgid ""
"Changing the language will refresh the page to apply the new language "
"settings."
msgstr ""

#: templates/job_preview_publish.html:7 templates/job_preview_publish.html:162
msgid "Publish Job"
msgstr ""

#: templates/job_preview_publish.html:11
msgid "Final Review"
msgstr ""

#: templates/job_preview_publish.html:12
msgid "Please review the job details before publishing."
msgstr ""

#: templates/job_preview_publish.html:32
msgid "Loading job description..."
msgstr ""

#: templates/job_preview_publish.html:39
msgid "Publish To"
msgstr ""

#: templates/job_preview_publish.html:41
msgid ""
"Select the job portals where you want to publish this job posting. <br> <br> "
"<i> if the portal you want to publish to is grayed out, it means that you "
"have not yet adjusted the related configuration settings. </i>"
msgstr ""

#: templates/job_preview_publish.html:61
msgid ""
"Professional networking platform with over 750 million users worldwide. "
"Selecting this option will open a new tab for you to complete the job "
"posting."
msgstr ""

#: templates/job_preview_publish.html:82
msgid ""
"Job and company review site focusing on workplace transparency. Selecting "
"this option will open a new tab for you to complete the job posting."
msgstr ""

#: templates/job_preview_publish.html:103
#, python-format
msgid ""
"Specialized job platform for tech and creative professionals powered by "
"Workloupe. Workloupe is 100%% free to use."
msgstr ""

#: templates/job_preview_publish.html:125
msgid ""
"One of the biggest remote job focused job platforms in the world. Posting to "
"Himalayas is free. "
msgstr ""

#: templates/job_preview_publish.html:147
msgid ""
"PostJobFree has more than 7 million jobs, and it's free to post to. Their "
"job portal is focused on simplicity and ease of use."
msgstr ""

#: templates/job_preview_publish.html:580
msgid ""
"No job information found. Please go back and fill out the job details form. "
msgstr ""

#: templates/job_preview_publish.html:662
msgid "No job description found. Please go back and create a job description."
msgstr ""

#: templates/jobs.html:8
msgid "Job Listings"
msgstr ""

#: templates/jobs.html:18
msgid "Active Jobs"
msgstr ""

#: templates/jobs.html:28
msgid "Total Applicants"
msgstr ""

#: templates/jobs.html:38
msgid "Archived Jobs"
msgstr ""

#: templates/jobs.html:48
msgid "On-Hold Jobs"
msgstr ""

#: templates/jobs.html:61
msgid "All Departments"
msgstr ""

#: templates/jobs.html:71 templates/manage_permissions.html:212
#: templates/people.html:43
msgid "All Statuses"
msgstr ""

#: templates/jobs.html:79 templates/people.html:51 templates/people.html:90
msgid "Location"
msgstr ""

#: templates/jobs.html:81 templates/people.html:53
msgid "All Locations"
msgstr ""

#: templates/jobs.html:89
msgid "Posted Date"
msgstr ""

#: templates/jobs.html:91
msgid "All Time"
msgstr ""

#: templates/jobs.html:93 templates/people.html:65
msgid "This Week"
msgstr ""

#: templates/jobs.html:94 templates/people.html:66
msgid "This Month"
msgstr ""

#: templates/jobs.html:95
msgid "Last Month"
msgstr ""

#: templates/jobs.html:106 templates/people.html:76
msgid "Clear all filters"
msgstr ""

#: templates/jobs.html:135
msgid "Interviews"
msgstr ""

#: templates/jobs.html:142
msgid "Hired"
msgstr ""

#: templates/jobs.html:146
msgid "Days Open"
msgstr ""

#: templates/jobs.html:152
msgid "Closed on:"
msgstr ""

#: templates/jobs.html:152
msgid "Posted on:"
msgstr ""

#: templates/jobs.html:153
msgid "View Details"
msgstr ""

#: templates/jobs.html:166
msgid "No Job Postings Yet"
msgstr ""

#: templates/jobs.html:168
msgid ""
"You haven't published any job postings yet. Create your first job posting to "
"start attracting candidates."
msgstr ""

#: templates/jobs.html:172
msgid "Create Your First Job"
msgstr ""

#: templates/jobs.html:186 templates/people.html:174
msgid "Showing"
msgstr ""

#: templates/jobs.html:186 templates/people.html:174
msgid "of"
msgstr ""

#: templates/manage_permissions.html:11
msgid "Team & Invitations"
msgstr ""

#: templates/manage_permissions.html:12
msgid "Manage your recruitment team and invite new members"
msgstr ""

#: templates/manage_permissions.html:17 templates/manage_permissions.html:39
#: templates/manage_permissions.html:73 templates/settings.html:62
msgid "Invitations"
msgstr ""

#: templates/manage_permissions.html:29 templates/manage_permissions.html:69
msgid "Team Members"
msgstr ""

#: templates/manage_permissions.html:59
msgid "Administrators"
msgstr ""

#: templates/manage_permissions.html:84
msgid "Search team members..."
msgstr ""

#: templates/manage_permissions.html:88 templates/manage_permissions.html:218
msgid "Role:"
msgstr ""

#: templates/manage_permissions.html:90 templates/manage_permissions.html:220
msgid "All Roles"
msgstr ""

#: templates/manage_permissions.html:105 templates/manage_permissions.html:244
#: templates/people.html:87 templates/profile.html:49
#: templates/published_job_details.html:276
msgid "Name"
msgstr ""

#: templates/manage_permissions.html:109 templates/manage_permissions.html:248
#: templates/profile.html:57 templates/profile.html:101
#: templates/register.html:24
msgid "Email"
msgstr ""

#: templates/manage_permissions.html:120 templates/manage_permissions.html:267
#: templates/profile.html:80 templates/published_job_details.html:285
msgid "Actions"
msgstr ""

#: templates/manage_permissions.html:143
msgid "Deactivate"
msgstr ""

#: templates/manage_permissions.html:147
msgid "Activate"
msgstr ""

#: templates/manage_permissions.html:206
msgid "Search invitations..."
msgstr ""

#: templates/manage_permissions.html:210
msgid "Status:"
msgstr ""

#: templates/manage_permissions.html:231
msgid "Invite New Member"
msgstr ""

#: templates/manage_permissions.html:256
msgid "Sent Date"
msgstr ""

#: templates/manage_permissions.html:260
msgid "Expiry Date"
msgstr ""

#: templates/manage_permissions.html:305
msgid "No invitations found"
msgstr ""

#: templates/manage_permissions.html:353
msgid "Invite New Team Member"
msgstr ""

#: templates/manage_permissions.html:360
msgid "Recipient Information"
msgstr ""

#: templates/manage_permissions.html:364 templates/profile.html:93
msgid "First Name"
msgstr ""

#: templates/manage_permissions.html:365
msgid "Enter first name"
msgstr ""

#: templates/manage_permissions.html:369 templates/profile.html:97
msgid "Last Name"
msgstr ""

#: templates/manage_permissions.html:370
msgid "Enter last name"
msgstr ""

#: templates/manage_permissions.html:375 templates/signin.html:17
msgid "Email Address"
msgstr ""

#: templates/manage_permissions.html:376
msgid "Enter email address"
msgstr ""

#: templates/manage_permissions.html:383 templates/manage_permissions.html:443
msgid "Select a role"
msgstr ""

#: templates/manage_permissions.html:384 templates/manage_permissions.html:444
msgid "Administrator"
msgstr ""

#: templates/manage_permissions.html:385 templates/manage_permissions.html:437
#: templates/manage_permissions.html:445
msgid "Recruiter"
msgstr ""

#: templates/manage_permissions.html:386 templates/manage_permissions.html:446
msgid "Hiring Manager"
msgstr ""

#: templates/manage_permissions.html:387 templates/manage_permissions.html:447
msgid "Interviewer"
msgstr ""

#: templates/manage_permissions.html:388 templates/manage_permissions.html:448
msgid "Read Only"
msgstr ""

#: templates/manage_permissions.html:395
msgid "Permissions"
msgstr ""

#: templates/manage_permissions.html:396
msgid ""
"Permissions are determined by the selected role. You can customize them "
"after the user has accepted the invitation."
msgstr ""

#: templates/manage_permissions.html:400
msgid "Role Descriptions:"
msgstr ""

#: templates/manage_permissions.html:402
msgid "Administrator: Full access to all system features and settings."
msgstr ""

#: templates/manage_permissions.html:403
msgid "Recruiter: Manage job postings, candidates, and interviews."
msgstr ""

#: templates/manage_permissions.html:404
msgid "Hiring Manager: Review candidates and make hiring decisions."
msgstr ""

#: templates/manage_permissions.html:405
msgid "Interviewer: Conduct interviews and provide feedback."
msgstr ""

#: templates/manage_permissions.html:406
msgid "Read Only: View-only access to recruitment data."
msgstr ""

#: templates/manage_permissions.html:414
msgid "Send Invitation"
msgstr ""

#: templates/manage_permissions.html:425 templates/manage_permissions.html:464
msgid "Change Role"
msgstr ""

#: templates/manage_permissions.html:431
msgid "Team Member"
msgstr ""

#: templates/manage_permissions.html:436
msgid "Current Role"
msgstr ""

#: templates/manage_permissions.html:441
msgid "New Role*"
msgstr ""

#: templates/manage_permissions.html:453
msgid "Reason for Change (Optional)"
msgstr ""

#: templates/manage_permissions.html:454
msgid "Provide a reason for this role change"
msgstr ""

#: templates/manage_permissions.html:459
msgid ""
"Changing roles will update the user's permissions. They will be notified of "
"this change."
msgstr ""

#: templates/navbar.html:31
msgid "Feed"
msgstr ""

#: templates/navbar.html:94
msgid "Employee"
msgstr ""

#: templates/navbar.html:100
msgid "Profile"
msgstr ""

#: templates/navbar.html:109
msgid "Logout"
msgstr ""

#: templates/navbar.html:118
msgid "Guest User"
msgstr ""

#: templates/navbar.html:121
msgid "Not logged in"
msgstr ""

#: templates/navbar.html:126 templates/signin.html:97
msgid "Sign In"
msgstr ""

#: templates/people.html:10
msgid "Applicant Tracking"
msgstr ""

#: templates/people.html:14 templates/people.html:1034
msgid "Refresh Applicants"
msgstr ""

#: templates/people.html:18
msgid "Search applicants..."
msgstr ""

#: templates/people.html:33
msgid "All Positions"
msgstr ""

#: templates/people.html:63
msgid "All Dates"
msgstr ""

#: templates/people.html:91
msgid "Experience (Years)"
msgstr ""

#: templates/people.html:92 templates/published_job_details.html:282
msgid "Score"
msgstr ""

#: templates/people.html:93
msgid "Applied On"
msgstr ""

#: templates/people.html:94
msgid "Action"
msgstr ""

#: templates/people.html:139 templates/published_job_details.html:343
msgid "View"
msgstr ""

#: templates/people.html:154 templates/published_job_details.html:246
msgid "No Applicants Yet"
msgstr ""

#: templates/people.html:156
msgid ""
"Nobody has applied to your job postings yet. Once candidates start applying, "
"you'll see them here."
msgstr ""

#: templates/people.html:160
msgid "View Job Postings"
msgstr ""

#: templates/people.html:163
msgid "Create New Job"
msgstr ""

#: templates/people.html:174
msgid "applicants"
msgstr ""

#: templates/people.html:227
msgid "Show"
msgstr ""

#: templates/people.html:234
msgid "per page"
msgstr ""

#: templates/people.html:969
msgid "Processing..."
msgstr ""

#: templates/people.html:1015
msgid "Success!"
msgstr ""

#: templates/people.html:1022 templates/workloupe_platform.html:1119
msgid "Error:"
msgstr ""

#: templates/people.html:1027
msgid "An error occurred while processing applications. Please try again."
msgstr ""

#: templates/profile.html:10
msgid "Your Profile"
msgstr ""

#: templates/profile.html:29
msgid "Change Photo"
msgstr ""

#: templates/profile.html:32
msgid "Profile Activity"
msgstr ""

#: templates/profile.html:33
msgid "Last Login:"
msgstr ""

#: templates/profile.html:36
msgid "Account Created:"
msgstr ""

#: templates/profile.html:46 templates/profile.html:91
msgid "Personal Information"
msgstr ""

#: templates/profile.html:63 templates/profile.html:109
#: templates/workloupe_platform.html:28
msgid "Company Information"
msgstr ""

#: templates/profile.html:83 templates/profile.html:163
#: templates/profile.html:185
msgid "Change Password"
msgstr ""

#: templates/profile.html:105
msgid "Phone"
msgstr ""

#: templates/profile.html:124
msgid "Save Changes"
msgstr ""

#: templates/profile.html:139
msgid "Upload Profile Photo"
msgstr ""

#: templates/profile.html:146
msgid "Choose a photo (PNG or JPEG only)"
msgstr ""

#: templates/profile.html:153
msgid "Upload"
msgstr ""

#: templates/profile.html:170
msgid "Current Password"
msgstr ""

#: templates/profile.html:174
msgid "New Password"
msgstr ""

#: templates/profile.html:178
msgid "Confirm New Password"
msgstr ""

#: templates/published_job_details.html:58
msgid "Notification"
msgstr ""

#: templates/published_job_details.html:82
msgid "Total Applicants:"
msgstr ""

#: templates/published_job_details.html:87
msgid "Published At:"
msgstr ""

#: templates/published_job_details.html:111
msgid "Bulk Communication"
msgstr ""

#: templates/published_job_details.html:124
msgid "Expert Support Options"
msgstr ""

#: templates/published_job_details.html:136
msgid "Post on LinkedIn"
msgstr ""

#: templates/published_job_details.html:148
#: templates/published_job_details.html:641
msgid "Change Vacancy Status"
msgstr ""

#: templates/published_job_details.html:162
msgid "Applicants Over Time"
msgstr ""

#: templates/published_job_details.html:170
msgid "No Application Data Yet"
msgstr ""

#: templates/published_job_details.html:171
msgid ""
"Once candidates start applying, you'll see application trends over time here."
msgstr ""

#: templates/published_job_details.html:184
msgid "Number of Applicants by Job Portal"
msgstr ""

#: templates/published_job_details.html:191
msgid "No Portal Data Yet"
msgstr ""

#: templates/published_job_details.html:192
msgid ""
"When applications come in, you'll see which job portals are most effective "
"here."
msgstr ""

#: templates/published_job_details.html:202
msgid "Distribution of Applicants by Status"
msgstr ""

#: templates/published_job_details.html:209
msgid "No Status Data Yet"
msgstr ""

#: templates/published_job_details.html:210
msgid ""
"Application status distribution will appear here as you review candidates."
msgstr ""

#: templates/published_job_details.html:225
msgid "Top Applicants"
msgstr ""

#: templates/published_job_details.html:233
msgid "View All Applicants"
msgstr ""

#: templates/published_job_details.html:249
msgid ""
"Don't worry! Once candidates start applying to this position, they will "
"appear here. You can track their progress, review their profiles, and manage "
"the hiring process."
msgstr ""

#: templates/published_job_details.html:256
msgid "What happens next?"
msgstr ""

#: templates/published_job_details.html:259
msgid "Candidates will apply through your job posting"
msgstr ""

#: templates/published_job_details.html:260
msgid "You'll see their profiles and CVs here"
msgstr ""

#: templates/published_job_details.html:261
msgid "You can review, rate, and manage applications"
msgstr ""

#: templates/published_job_details.html:262
msgid "Use the communication tools to contact candidates"
msgstr ""

#: templates/published_job_details.html:332
msgid "Not Rated"
msgstr ""

#: templates/published_job_details.html:360
msgid "Request Support From Experts"
msgstr ""

#: templates/published_job_details.html:365
msgid ""
"We can provide vetted candidates from our talent pool, or help you during "
"the technical interviews to pick best fit for your expectations."
msgstr ""

#: templates/published_job_details.html:370
msgid "Enter Details"
msgstr ""

#: templates/published_job_details.html:381
msgid "Request Candidates"
msgstr ""

#: templates/published_job_details.html:382
msgid "Request Interview Help"
msgstr ""

#: templates/published_job_details.html:396
msgid "Send Bulk Mail to Applicants"
msgstr ""

#: templates/published_job_details.html:401
msgid ""
"Use this form to send bulk emails to applicants based on their application "
"statuses."
msgstr ""

#: templates/published_job_details.html:406
msgid "Select Application Status"
msgstr ""

#: templates/published_job_details.html:413
msgid "Select a status"
msgstr ""

#: templates/published_job_details.html:420
msgid "Email Subject"
msgstr ""

#: templates/published_job_details.html:439
msgid "Enter internal notes for your team (optional)"
msgstr ""

#: templates/published_job_details.html:443
msgid "(sent to candidates)"
msgstr ""

#: templates/published_job_details.html:450
msgid "Enter your email message"
msgstr ""

#: templates/published_job_details.html:462
msgid "Send notification emails to candidates"
msgstr ""

#: templates/published_job_details.html:466
msgid "Send Emails"
msgstr ""

#: templates/published_job_details.html:482
msgid "Post this job on LinkedIn"
msgstr ""

#: templates/published_job_details.html:489
msgid ""
"Follow these simple steps to post your job on LinkedIn and reach more "
"candidates."
msgstr ""

#: templates/published_job_details.html:496
msgid "Navigate to LinkedIn Job Posting"
msgstr ""

#: templates/published_job_details.html:499
msgid "Go to"
msgstr ""

#: templates/published_job_details.html:499
msgid "LinkedIn Job Posting Page"
msgstr ""

#: templates/published_job_details.html:507
msgid "Copy Job Title"
msgstr ""

#: templates/published_job_details.html:509
msgid "Copy and paste the job title below:"
msgstr ""

#: templates/published_job_details.html:526
msgid "Configure Job Settings"
msgstr ""

#: templates/published_job_details.html:529
msgid "Select 'Use my own description' option and configure job details:"
msgstr ""

#: templates/published_job_details.html:531
msgid "Reference - Your job settings:"
msgstr ""

#: templates/published_job_details.html:533
msgid "Location:"
msgstr ""

#: templates/published_job_details.html:534
msgid "Work Schedule:"
msgstr ""

#: templates/published_job_details.html:535
msgid "Office Schedule:"
msgstr ""

#: templates/published_job_details.html:545
msgid "Copy Job Description"
msgstr ""

#: templates/published_job_details.html:548
msgid "Copy and paste the job description below:"
msgstr ""

#: templates/published_job_details.html:551
msgid ""
"Note: Delete any placeholder text in LinkedIn's editor before pasting. You "
"may have to re-apply some of the styling."
msgstr ""

#: templates/published_job_details.html:570
msgid "Configure Application Management"
msgstr ""

#: templates/published_job_details.html:574
msgid "Click 'Continue' button"
msgstr ""

#: templates/published_job_details.html:575
msgid "Find 'Manage applicants' option and click the pencil icon to edit"
msgstr ""

#: templates/published_job_details.html:576
msgid ""
"Change 'Manage Applications' from 'On LinkedIn' to 'On an External Website'"
msgstr ""

#: templates/published_job_details.html:577
msgid "Copy and paste the application URL below:"
msgstr ""

#: templates/published_job_details.html:596
msgid "Review Qualifications"
msgstr ""

#: templates/published_job_details.html:599
msgid "Review and customize the ideal qualifications section:"
msgstr ""

#: templates/published_job_details.html:601
msgid "Reference - Skills from your job:"
msgstr ""

#: templates/published_job_details.html:615
msgid "Finalize and Publish"
msgstr ""

#: templates/published_job_details.html:619
msgid "Confirm your identity using your work email if required"
msgstr ""

#: templates/published_job_details.html:620
msgid "Choose between free or promoted posting (Recommended: Free)"
msgstr ""

#: templates/published_job_details.html:621
msgid "Click 'Post Job' button"
msgstr ""

#: templates/published_job_details.html:622
msgid "Your job is now live on LinkedIn!"
msgstr ""

#: templates/published_job_details.html:629
msgid ""
"Need help? Contact our support team if you encounter any issues during the "
"posting process."
msgstr ""

#: templates/published_job_details.html:646
msgid "Current Status:"
msgstr ""

#: templates/published_job_details.html:652
msgid "Changing the status will affect the visibility of the vacancy."
msgstr ""

#: templates/published_job_details.html:655
msgid ""
"The vacancy will no longer exist on boards and be closed but be accesible "
"internally."
msgstr ""

#: templates/published_job_details.html:656
msgid "The vacancy will stop accepting new applications until changed."
msgstr ""

#: templates/published_job_details.html:657
msgid "The vacancy will be re-opened for new applications."
msgstr ""

#: templates/published_job_details.html:658
msgid "The vacancy will be permanently deleted. This action cannot be undone."
msgstr ""

#: templates/published_job_details.html:664
msgid "Select New Status"
msgstr ""

#: templates/published_job_details.html:687
msgid "Confirm Status"
msgstr ""

#: templates/register.html:15
msgid "This invitation has expired or already been used."
msgstr ""

#: templates/register.html:18
msgid "Hello"
msgstr ""

#: templates/register.html:18
msgid "you've been invited to join"
msgstr ""

#: templates/register.html:18
msgid "as a"
msgstr ""

#: templates/register.html:29
msgid "Create Password"
msgstr ""

#: templates/register.html:34
msgid "Confirm Password"
msgstr ""

#: templates/register.html:39
msgid "Complete Registration"
msgstr ""

#: templates/register.html:58
msgid "Passwords do not match"
msgstr ""

#: templates/registration_complete.html:14
msgid "Registration Complete!"
msgstr ""

#: templates/registration_complete.html:15
msgid ""
"Your account has been created successfully. You can now log in to access the "
"system."
msgstr ""

#: templates/registration_complete.html:17
msgid "Go to Login"
msgstr ""

#: templates/settings.html:10
msgid "Configure your recruitment workflow and manage your ATS settings"
msgstr ""

#: templates/settings.html:21
msgid ""
"Configure default options for job creation including work schedules, office "
"locations, and role titles."
msgstr ""

#: templates/settings.html:23
msgid "Define company work schedules"
msgstr ""

#: templates/settings.html:24
msgid "Set up office locations"
msgstr ""

#: templates/settings.html:25
msgid "Standardize role titles"
msgstr ""

#: templates/settings.html:26
msgid "Configure office schedule options"
msgstr ""

#: templates/settings.html:29
msgid "Manage Preferences"
msgstr ""

#: templates/settings.html:42
msgid ""
"Create, edit, and manage job description templates to streamline your job "
"posting process."
msgstr ""

#: templates/settings.html:44
msgid "Build reusable job templates"
msgstr ""

#: templates/settings.html:45
msgid "Save time on repetitive descriptions"
msgstr ""

#: templates/settings.html:46
msgid "Maintain consistent job postings"
msgstr ""

#: templates/settings.html:47
msgid "Organize templates by department"
msgstr ""

#: templates/settings.html:50
msgid "Manage Templates"
msgstr ""

#: templates/settings.html:63
msgid ""
"Invite team members to collaborate on your recruitment process and manage "
"user access."
msgstr ""

#: templates/settings.html:65
msgid "Add colleagues to your ATS"
msgstr ""

#: templates/settings.html:66
msgid "Set user permissions"
msgstr ""

#: templates/settings.html:67
msgid "Track invitation status"
msgstr ""

#: templates/settings.html:68
msgid "Manage team collaboration"
msgstr ""

#: templates/settings.html:71
msgid "Manage Invitations"
msgstr ""

#: templates/settings.html:84
msgid "Job Portals"
msgstr ""

#: templates/settings.html:85
msgid ""
"Configure connections to external job boards and manage API credentials for "
"job publishing."
msgstr ""

#: templates/settings.html:87
msgid "Connect to major job boards"
msgstr ""

#: templates/settings.html:88
msgid "Manage API tokens securely"
msgstr ""

#: templates/settings.html:89
msgid "Customize portal preferences"
msgstr ""

#: templates/settings.html:90
msgid "Track portal integration status"
msgstr ""

#: templates/settings.html:93
msgid "(Coming Soon!)"
msgstr ""

#: templates/settings.html:106
msgid "Careers Page & Workloupe Configurations"
msgstr ""

#: templates/settings.html:107
msgid ""
"Adjust the look and feel of your company careers page and workloupe profile "
"to match your branding."
msgstr ""

#: templates/settings.html:109
msgid "Upload company photos"
msgstr ""

#: templates/settings.html:110
msgid "Pick your colours"
msgstr ""

#: templates/settings.html:111
msgid "Choose the components"
msgstr ""

#: templates/settings.html:112
msgid "Upload your company logo and banner"
msgstr ""

#: templates/settings.html:115
msgid "Manage Carees Page & Workloupe Configurations"
msgstr ""

#: templates/settings.html:126
msgid "Need Help?"
msgstr ""

#: templates/settings.html:127
msgid ""
"Our support team is ready to assist you with any questions about configuring "
"your ATS."
msgstr ""

#: templates/settings.html:128
msgid "Contact Support"
msgstr ""

#: templates/signin.html:9
msgid "Welcome Back"
msgstr ""

#: templates/signin.html:10
msgid "Sign in to your account"
msgstr ""

#: templates/signin.html:24
msgid "Enter your email"
msgstr ""

#: templates/signin.html:38
msgid "Password"
msgstr ""

#: templates/signin.html:45
msgid "Enter your password"
msgstr ""

#: templates/signin.html:72
msgid "Security Check"
msgstr ""

#: templates/signin.html:72
msgid "What is"
msgstr ""

#: templates/signin.html:79
msgid "Enter the answer"
msgstr ""

#: templates/signin.html:89
msgid "Remember me"
msgstr ""

#: templates/signin.html:92
msgid "Forgot password?"
msgstr ""

#: templates/signin.html:106
msgid "Don't have an account?"
msgstr ""

#: templates/signin.html:108
msgid "Contact us"
msgstr ""

#: templates/wordpress_integration.html:14
msgid "WordPress Integration"
msgstr ""

#: templates/wordpress_integration.html:15
msgid "Configure your WordPress careers page"
msgstr ""

#: templates/wordpress_integration.html:23
msgid "WordPress Setup"
msgstr ""

#: templates/wordpress_integration.html:28
msgid "Choose your preferred WordPress integration method"
msgstr ""

#: templates/wordpress_integration.html:32
msgid "Integration Method"
msgstr ""

#: templates/wordpress_integration.html:34
msgid "Shortcode (Recommended)"
msgstr ""

#: templates/wordpress_integration.html:35
#: templates/wordpress_integration.html:187
msgid "WordPress Widget"
msgstr ""

#: templates/wordpress_integration.html:36
#: templates/wordpress_integration.html:201
msgid "Custom Plugin"
msgstr ""

#: templates/wordpress_integration.html:63
msgid "Design Settings"
msgstr ""

#: templates/wordpress_integration.html:72
msgid "WordPress Theme Style"
msgstr ""

#: templates/wordpress_integration.html:74
msgid "Inherit from Theme"
msgstr ""

#: templates/wordpress_integration.html:84
msgid "Responsive Design"
msgstr ""

#: templates/wordpress_integration.html:97
msgid "Jobs Per Page"
msgstr ""

#: templates/wordpress_integration.html:109
msgid "Show Job Filters"
msgstr ""

#: templates/wordpress_integration.html:116
msgid "Show Search Box"
msgstr ""

#: templates/wordpress_integration.html:123
msgid "Show Pagination"
msgstr ""

#: templates/wordpress_integration.html:132
msgid "Generate WordPress Code"
msgstr ""

#: templates/wordpress_integration.html:141
msgid "Preview & Instructions"
msgstr ""

#: templates/wordpress_integration.html:144
msgid "Preview"
msgstr ""

#: templates/wordpress_integration.html:147
msgid "Instructions"
msgstr ""

#: templates/wordpress_integration.html:167
msgid "Shortcode Integration"
msgstr ""

#: templates/wordpress_integration.html:169
msgid "Recommended Method"
msgstr ""

#: templates/wordpress_integration.html:169
msgid "Easy to use and works with any WordPress theme"
msgstr ""

#: templates/wordpress_integration.html:172
msgid "Copy the shortcode below"
msgstr ""

#: templates/wordpress_integration.html:173
msgid "Go to your WordPress admin panel"
msgstr ""

#: templates/wordpress_integration.html:174
msgid "Edit the page where you want to display jobs"
msgstr ""

#: templates/wordpress_integration.html:175
msgid "Paste the shortcode in the content area"
msgstr ""

#: templates/wordpress_integration.html:176
msgid "Save and publish the page"
msgstr ""

#: templates/wordpress_integration.html:189
msgid "Perfect for sidebars and widget areas"
msgstr ""

#: templates/wordpress_integration.html:192
msgid "Go to Appearance > Widgets in your WordPress admin"
msgstr ""

#: templates/wordpress_integration.html:193
msgid "Find the 'Workloupe Careers' widget"
msgstr ""

#: templates/wordpress_integration.html:194
msgid "Drag it to your desired widget area"
msgstr ""

#: templates/wordpress_integration.html:195
msgid "Configure the widget settings"
msgstr ""

#: templates/wordpress_integration.html:196
msgid "Save the widget"
msgstr ""

#: templates/wordpress_integration.html:203
msgid "Advanced option - requires technical knowledge"
msgstr ""

#: templates/wordpress_integration.html:206
msgid "Download the custom plugin file"
msgstr ""

#: templates/wordpress_integration.html:207
msgid "Upload it to your WordPress plugins directory"
msgstr ""

#: templates/wordpress_integration.html:208
msgid "Activate the plugin in WordPress admin"
msgstr ""

#: templates/wordpress_integration.html:209
msgid "Configure the plugin settings"
msgstr ""

#: templates/wordpress_integration.html:210
msgid "Use shortcodes or widgets as needed"
msgstr ""

#: templates/wordpress_integration.html:214
msgid "Download Plugin"
msgstr ""

#: templates/wordpress_integration.html:231
msgid "WordPress Integration Code"
msgstr ""

#: templates/wordpress_integration.html:238
msgid "Copy the code below and follow the integration instructions."
msgstr ""

#: templates/wordpress_integration.html:243
msgid "WordPress Code"
msgstr ""

#: templates/wordpress_integration.html:256
msgid "Download Files"
msgstr ""

#: templates/workloupe_platform.html:13
msgid "Workloupe Platform Setup"
msgstr ""

#: templates/workloupe_platform.html:14
msgid "Create and manage your company profile on workloupe.com"
msgstr ""

#: templates/workloupe_platform.html:18
msgid ""
"Update your existing profile information below. All changes will be saved "
"automatically."
msgstr ""

#: templates/workloupe_platform.html:36
msgid "Company name is required"
msgstr ""

#: templates/workloupe_platform.html:41
msgid "Company Email"
msgstr ""

#: templates/workloupe_platform.html:43
msgid "Please enter a valid email address"
msgstr ""

#: templates/workloupe_platform.html:51
msgid "Phone Number"
msgstr ""

#: templates/workloupe_platform.html:53
msgid "Include country code (e.g., ******-123-4567)"
msgstr ""

#: templates/workloupe_platform.html:58
msgid "Website"
msgstr ""

#: templates/workloupe_platform.html:60
msgid "Please enter a valid website URL"
msgstr ""

#: templates/workloupe_platform.html:66
msgid "Company Address"
msgstr ""

#: templates/workloupe_platform.html:67
msgid "Full company address including city, state, country"
msgstr ""

#: templates/workloupe_platform.html:71
msgid "Office Locations"
msgstr ""

#: templates/workloupe_platform.html:72
msgid "Separate multiple locations with | (e.g., New York | London | Remote)"
msgstr ""

#: templates/workloupe_platform.html:73
msgid "Use | to separate multiple office locations"
msgstr ""

#: templates/workloupe_platform.html:77
msgid "Company Description"
msgstr ""

#: templates/workloupe_platform.html:78
msgid ""
"Describe your company, mission, values, and what makes it special. This will "
"be prominently displayed on your profile."
msgstr ""

#: templates/workloupe_platform.html:79
msgid "Company description is required"
msgstr ""

#: templates/workloupe_platform.html:80
msgid "Minimum 50 characters recommended for better visibility"
msgstr ""

#: templates/workloupe_platform.html:88
msgid "Company Details"
msgstr ""

#: templates/workloupe_platform.html:94
msgid "Industry"
msgstr ""

#: templates/workloupe_platform.html:96
msgid "Select Industry"
msgstr ""

#: templates/workloupe_platform.html:97
msgid "Technology"
msgstr ""

#: templates/workloupe_platform.html:98
msgid "Healthcare"
msgstr ""

#: templates/workloupe_platform.html:99
msgid "Finance"
msgstr ""

#: templates/workloupe_platform.html:100
msgid "Education"
msgstr ""

#: templates/workloupe_platform.html:101
msgid "Manufacturing"
msgstr ""

#: templates/workloupe_platform.html:102
msgid "Retail"
msgstr ""

#: templates/workloupe_platform.html:103
msgid "Consulting"
msgstr ""

#: templates/workloupe_platform.html:104
msgid "Marketing & Advertising"
msgstr ""

#: templates/workloupe_platform.html:105
msgid "Real Estate"
msgstr ""

#: templates/workloupe_platform.html:106
msgid "Non-profit"
msgstr ""

#: templates/workloupe_platform.html:107
msgid "Other"
msgstr ""

#: templates/workloupe_platform.html:113
msgid "Company Size"
msgstr ""

#: templates/workloupe_platform.html:114
msgid "Number of employees"
msgstr ""

#: templates/workloupe_platform.html:115
msgid "Please enter a valid number of employees"
msgstr ""

#: templates/workloupe_platform.html:116
msgid "Enter the total number of employees in your company"
msgstr ""

#: templates/workloupe_platform.html:122
msgid "Social Media Links"
msgstr ""

#: templates/workloupe_platform.html:122
msgid "Optional"
msgstr ""

#: templates/workloupe_platform.html:128
msgid "Please enter a valid LinkedIn URL"
msgstr ""

#: templates/workloupe_platform.html:133
msgid "Please enter a valid Instagram URL"
msgstr ""

#: templates/workloupe_platform.html:140
msgid "Please enter a valid Twitter URL"
msgstr ""

#: templates/workloupe_platform.html:145
msgid "Please enter a valid Github URL"
msgstr ""

#: templates/workloupe_platform.html:152
msgid "Please enter a valid Facebook URL"
msgstr ""

#: templates/workloupe_platform.html:157
msgid "Please enter a valid Glassdoor URL"
msgstr ""

#: templates/workloupe_platform.html:161
msgid ""
"Add your company's social media profiles to increase visibility. URLs will "
"be validated automatically."
msgstr ""

#: templates/workloupe_platform.html:169
msgid "Branding Assets"
msgstr ""

#: templates/workloupe_platform.html:169
msgid "(Publicly Visible)"
msgstr ""

#: templates/workloupe_platform.html:177
msgid "Recommended: 300x300px, PNG or JPG. Max 3MB."
msgstr ""

#: templates/workloupe_platform.html:181
msgid "Current logo - upload new file to replace"
msgstr ""

#: templates/workloupe_platform.html:185 templates/workloupe_platform.html:1165
msgid "No logo uploaded"
msgstr ""

#: templates/workloupe_platform.html:193
msgid "Company Banner"
msgstr ""

#: templates/workloupe_platform.html:195
msgid "Recommended: 1200x400px, PNG or JPG. Max 3MB."
msgstr ""

#: templates/workloupe_platform.html:199
msgid "Current banner - upload new file to replace"
msgstr ""

#: templates/workloupe_platform.html:203 templates/workloupe_platform.html:1172
msgid "No banner uploaded"
msgstr ""

#: templates/workloupe_platform.html:213
msgid ""
"If you don't have online versions of your logo or banner, you can upload "
"them here. They will be stored securely and made publicly accessible for "
"your profile."
msgstr ""

#: templates/workloupe_platform.html:221
msgid "Company Gallery"
msgstr ""

#: templates/workloupe_platform.html:226
msgid "Important:"
msgstr ""

#: templates/workloupe_platform.html:226
msgid ""
"Photos will be public once you publish your profile. Maximum 50 photos "
"allowed. We reserve the right to remove inappropriate content. Only upload "
"professional, work-appropriate images."
msgstr ""

#: templates/workloupe_platform.html:232
msgid "Current Photos"
msgstr ""

#: templates/workloupe_platform.html:238
msgid "Loading existing photos..."
msgstr ""

#: templates/workloupe_platform.html:246
msgid "Upload New Photos"
msgstr ""

#: templates/workloupe_platform.html:249
msgid ""
"Select multiple photos (JPG, PNG, WebP). Max 5MB per photo. Showcase your "
"company culture, office, team, events, etc."
msgstr ""

#: templates/workloupe_platform.html:260
msgid "Uploading photos..."
msgstr ""

#: templates/workloupe_platform.html:268
msgid "Reset Form"
msgstr ""

#: templates/workloupe_platform.html:272
msgid "Save & Publish Profile"
msgstr ""

#: templates/workloupe_platform.html:279
msgid ""
"Your data is secure and will only be used for your public company profile"
msgstr ""

#: templates/workloupe_platform.html:295
msgid "Profile Published Successfully!"
msgstr ""

#: templates/workloupe_platform.html:302
msgid "Your company is now live on Workloupe!"
msgstr ""

#: templates/workloupe_platform.html:303
msgid ""
"Your company profile has been created and published on workloupe.com. "
"Candidates can now discover your company and apply to your jobs."
msgstr ""

#: templates/workloupe_platform.html:307
msgid "Your Profile URL:"
msgstr ""

#: templates/workloupe_platform.html:321
msgid "Share Your Profile"
msgstr ""

#: templates/workloupe_platform.html:322
msgid "Share this link with candidates and on social media"
msgstr ""

#: templates/workloupe_platform.html:330
msgid "Update Anytime"
msgstr ""

#: templates/workloupe_platform.html:331
msgid "Return to this page to update your profile information"
msgstr ""

#: templates/workloupe_platform.html:341
msgid "View Live Profile"
msgstr ""

#: templates/workloupe_platform.html:825
msgid "New logo selected - will be uploaded on save"
msgstr ""

#: templates/workloupe_platform.html:842
msgid "New banner selected - will be uploaded on save"
msgstr ""

#: templates/workloupe_platform.html:850
msgid "Please select a valid image file (JPG, PNG, WebP)"
msgstr ""

#: templates/workloupe_platform.html:856
msgid "File size must be less than"
msgstr ""

#: templates/workloupe_platform.html:868
msgid "No existing photos found"
msgstr ""

#: templates/workloupe_platform.html:889
msgid "Remove image"
msgstr ""

#: templates/workloupe_platform.html:919
msgid "Failed to remove image. Please try again."
msgstr ""

#: templates/workloupe_platform.html:924
msgid "An error occurred while removing the image."
msgstr ""

#: templates/workloupe_platform.html:953
msgid "You can only upload"
msgstr ""

#: templates/workloupe_platform.html:953
msgid "more photos. Maximum"
msgstr ""

#: templates/workloupe_platform.html:953
msgid "photos allowed."
msgstr ""

#: templates/workloupe_platform.html:1042
msgid "Please fix the validation errors before submitting."
msgstr ""

#: templates/workloupe_platform.html:1097
msgid "Publishing..."
msgstr ""

#: templates/workloupe_platform.html:1119
msgid "Unknown error occurred"
msgstr ""

#: templates/workloupe_platform.html:1124
msgid "An error occurred while publishing your profile. Please try again."
msgstr ""

#: templates/workloupe_platform.html:1153
msgid "Failed to copy URL. Please copy it manually."
msgstr ""

#: templates/workloupe_platform.html:1158
msgid ""
"Are you sure you want to reset the form? All unsaved changes will be lost."
msgstr ""
