# Turkish translation for Canvider
# Copyright (C) 2025 Canvider
# This file is distributed under the same license as the Canvider package.
#
msgid ""
msgstr ""
"Project-Id-Version: Canvider 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-07-26 23:39+0000\n"
"PO-Revision-Date: 2025-06-23 14:37+0000\n"
"Last-Translator: Canvider Team\n"
"Language-Team: Turkish\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"

# Email subjects
#: feed/email_utils.py:202
#, python-format
msgid "Application Status Update - %(job_title)s"
msgstr "Başvuru Durumu Güncelleme - %(job_title)s"

#: feed/email_utils.py:247
#, python-format
msgid "Meeting Invitation - %(job_title)s"
msgstr "Top<PERSON>ı Daveti - %(job_title)s"

#: feed/email_utils.py:329
#, python-format
msgid "Application Received - %(job_title)s"
msgstr "Başvuru Alındı - %(job_title)s"

#: feed/email_utils.py:376
#, python-format
msgid "Invitation: Join %(company_name)s at Canvider ATS"
msgstr "Davet: %(company_name)s'e Canvider ATS'de katılın"

#: feed/views.py:154
#, python-format
msgid "<strong>%(name)s</strong> applied for <strong>%(position)s</strong>"
msgstr ""
"<strong>%(name)s</strong>, <strong>%(position)s</strong> pozisyonuna başvurdu"

#: feed/views.py:195
#, python-format
msgid ""
"<strong>%(name)s</strong> moved to <strong>%(state)s</strong> for "
"<strong>%(position)s</strong>"
msgstr ""
" <strong>%(name)s</strong>, başvurduğu <strong>%(position)s</strong> "
"pozisyonu için <strong>%(state)s</strong> durumuna taşındı "

#: feed/views.py:225
#, python-format
msgid "A new vacancy <strong>%(vacancy_title)s</strong> is published"
msgstr "Yeni iş ilanı: <strong>%(vacancy_title)s</strong> yayınlandı"

#: feed/views.py:249
msgid "New comment on application of"
msgstr "Yeni yorum eklendi:"

#: feed/views.py:251
msgid "New comment on application ID"
msgstr "Yeni yorum eklendi:"

#: feed/views.py:267
msgid "now"
msgstr "az önce"

#: feed/views.py:270
msgid "1 minute ago"
msgstr "1 dakika önce"

#: feed/views.py:273
msgid "minutes ago"
msgstr "dakikalar önce"

#: feed/views.py:276
msgid "1 hour ago"
msgstr "1 saat önce"

#: feed/views.py:280
msgid "hours ago"
msgstr "saatler önce"

#: feed/views.py:283
msgid "yesterday"
msgstr "dün"

#: feed/views.py:287
msgid "days ago"
msgstr "gün önce"

#: feed/views.py:290
msgid "last week"
msgstr "geçen hafta"

#: feed/views.py:294
msgid "weeks ago"
msgstr "haftalar önce"

#: feed/views.py:297
msgid "last month"
msgstr "Geçen Ay"

#: feed/views.py:301
msgid "months ago"
msgstr "aylar önce"

#: feed/views.py:306
msgid "last year"
msgstr "geçen yıl"

#: feed/views.py:308
msgid "years ago"
msgstr "yıllar önce"

#: feed/views.py:1045
msgid "Profile photo changed successfully!"
msgstr "Profil fotoğrafı başarıyla değiştirildi!"

#: feed/views.py:1050
msgid "Please select a photo."
msgstr "Lütfen bir fotoğraf seçin."

#: feed/views.py:1876
#, python-format
msgid "Language changed to %(language)s"
msgstr "Dil %(language)s olarak değiştirildi"

#: feed/views.py:1880
msgid "Invalid language selection"
msgstr "Geçersiz dil seçimi"

#: feed/views.py:1909 templates/feed.html:17
msgid "Dashboard"
msgstr "Panel"

#: feed/views.py:2079
msgid "Invitation mail sent successfully!"
msgstr "Davet e-postası başarıyla gönderildi!"

#: feed/views.py:2081 feed/views.py:2086 feed/views.py:2229
msgid "Failed to send the invitation. Please check the form."
msgstr "Davet gönderilemedi. Lütfen formu kontrol edin."

#: feed/views.py:2113
msgid "Passwords do not match."
msgstr "Şifreler eşleşmiyor."

#: feed/views.py:2144
msgid "No employer found to associate with this account."
msgstr "Bu hesapla ilişkilendirilecek işveren bulunamadı."

#: feed/views.py:2154
msgid "Registration completed successfully! You can now log in."
msgstr "Kayıt başarıyla tamamlandı! Artık giriş yapabilirsiniz."

#: feed/views.py:2158
#, python-format
msgid "Error creating account: %(error)s"
msgstr "Hesap oluşturulurken hata: %(error)s"

#: feed/views.py:2184
msgid "Access denied."
msgstr ""

#: feed/views.py:2211
msgid "Invitation sent successfully!"
msgstr "Davet başarıyla gönderildi!"

#: feed/views.py:2240
msgid "User removed successfully!"
msgstr "Kullanıcı başarıyla kaldırıldı!"

#: feed/views.py:2253
msgid "User status changed successfully!"
msgstr "Kullanıcı durumu başarıyla değiştirildi!"

#: feed/views.py:2405
msgid "Talent request sent successfully! Our team will get back to you soon."
msgstr ""
"Yetenek talebi başarıyla gönderildi! Ekibimiz en kısa sürede size dönüş "
"yapacak."

#: feed/views.py:2409
msgid "Invalid request method."
msgstr "Geçersiz istek yöntemi."

# Status update messages
#: feed/views.py:2462
msgid "Status updated and notification email sent to candidate."
msgstr "Durum güncellendi ve adaya bildirim e-postası gönderildi."

#: feed/views.py:2464 feed/views.py:2468
msgid "Status updated but email notification failed to send."
msgstr "Durum güncellendi ancak e-posta bildirimi gönderilemedi."

#: feed/views.py:3639
msgid ""
"Mail has been sent successfully, it will appear on this page after a few "
"minutes."
msgstr ""
"E-posta başarıyla gönderildi, birkaç dakika sonra bu sayfada görünecektir."

#: feed/views.py:3641
msgid "Failed to send email. Please try again."
msgstr "E-posta gönderilemedi. Lütfen tekrar deneyin."

#: feed/views.py:3646
#, python-format
msgid "Failed to send email: %(error)s"
msgstr "E-posta gönderilemedi: %(error)s"

#: feed/views.py:4604
msgid "Image URL and employer ID are required"
msgstr ""

#: feed/views.py:4636
#, python-format
msgid "Failed to remove image: %(error)s"
msgstr ""

#: feed/views.py:4641
msgid "Invalid request method"
msgstr "Geçersiz istek yöntemi"

#: templates/applicant_dev.html:30
msgid "Applied for:"
msgstr "Başvurduğu pozisyon:"

#: templates/applicant_dev.html:89 templates/applicant_dev.html:870
msgid "Schedule Interview"
msgstr "Görüşme Planla"

#: templates/applicant_dev.html:99
msgid "Change State"
msgstr "Durumu Değiştir"

#: templates/applicant_dev.html:114
msgid "Dashboard & AI"
msgstr "Panel & AI"

#: templates/applicant_dev.html:123
msgid "Candidate Background"
msgstr "Aday Hakkında"

#: templates/applicant_dev.html:132 templates/applicant_dev.html:565
#: templates/applicant_dev.html:583
msgid "Resume"
msgstr "Özgeçmiş"

#: templates/applicant_dev.html:141
msgid "Journey"
msgstr "Süreç"

#: templates/applicant_dev.html:150 templates/applicant_dev.html:785
msgid "Internal Comments"
msgstr "Dahili Notlar"

#: templates/applicant_dev.html:159 templates/applicant_dev.html:685
msgid "Emails"
msgstr "E-postalar"

#: templates/applicant_dev.html:167 templates/applicant_dev.html:854
msgid "Job Details"
msgstr "İş Detayları"

#: templates/applicant_dev.html:191
msgid "Profile Match Analysis"
msgstr "Profil Uyum Analizi"

#: templates/applicant_dev.html:199
msgid "Key Highlights"
msgstr "Öne Çıkan Özellikler"

#: templates/applicant_dev.html:225
msgid "AI analysis will provide candidate highlights."
msgstr "AI analizi aday öne çıkan özelliklerini sağlayacak."

#: templates/applicant_dev.html:235
msgid "Areas for Improvement"
msgstr "Gelişim Alanları"

#: templates/applicant_dev.html:272
msgid "Candidate Summary"
msgstr "Aday Özeti"

#: templates/applicant_dev.html:321
msgid ""
"Based on the AI analysis, when the resume is compared to the job "
"requirements,"
msgstr "AI analizine göre, özgeçmiş iş gereksinimleriyle karşılaştırıldığında,"

#: templates/applicant_dev.html:323
msgid "This candidate is an"
msgstr "Bu aday"

#: templates/applicant_dev.html:324
msgid "excellent match"
msgstr "mükemmel bir eşleşme"

#: templates/applicant_dev.html:326 templates/applicant_dev.html:329
#: templates/applicant_dev.html:332
msgid "This candidate is a"
msgstr "Bu aday"

#: templates/applicant_dev.html:327
msgid "good match"
msgstr "iyi bir eşleşme"

#: templates/applicant_dev.html:330
msgid "fair match"
msgstr "orta bir eşleşme"

#: templates/applicant_dev.html:333
msgid "weak match"
msgstr "zayıf bir eşleşme"

#: templates/applicant_dev.html:336
msgid "Analyze the CV with AI to see match details."
msgstr "Eşleşme detaylarını görmek için CV'yi AI ile analiz edin."

#: templates/applicant_dev.html:363
msgid "AI Analysis Available"
msgstr "AI Analizi Mevcut"

#: templates/applicant_dev.html:366
msgid "Leverage AI to analyze this candidate's CV against the job description."
msgstr ""
"Bu adayın CV'sini iş tanımına karşı analiz etmek için AI'dan yararlanın."

#: templates/applicant_dev.html:387
msgid "Analyze with CanviderAI"
msgstr "CanviderAI ile Analiz Et"

#: templates/applicant_dev.html:402
msgid "Analyzing CV..."
msgstr "CV analiz ediliyor..."

#: templates/applicant_dev.html:403
msgid "This may take a moment"
msgstr "Bu biraz zaman alabilir"

#: templates/applicant_dev.html:428 templates/applicant_dev.html:449
msgid "Analysis complete"
msgstr "Analiz tamamlandı"

#: templates/applicant_dev.html:460
msgid "Candidate Facts"
msgstr "Aday Bilgileri"

#: templates/applicant_dev.html:467
msgid "Applied Position"
msgstr "Başvurulan Pozisyon"

#: templates/applicant_dev.html:473
msgid "Candidate's Address"
msgstr "Adayın Adresi"

#: templates/applicant_dev.html:476 templates/applicant_dev.html:499
#: templates/applicant_dev.html:506 templates/applicant_dev.html:529
#: templates/applicant_dev.html:536 templates/people.html:119
#: templates/people.html:126 templates/people.html:133
msgid "Not analyzed"
msgstr "Analiz edilmedi"

#: templates/applicant_dev.html:480
#: templates/emails/application_confirmation.html:30
#: templates/emails/application_status_change.html:32
#: templates/emails/general_message.html:32 templates/people.html:61
msgid "Application Date"
msgstr "Başvuru Tarihi"

#: templates/applicant_dev.html:486
msgid "Application Portal"
msgstr "Başvuru Portalı"

#: templates/applicant_dev.html:496
msgid "Latest/Current Position"
msgstr "Güncel/En Son Pozisyon"

#: templates/applicant_dev.html:503
msgid "Latest/Current Employer"
msgstr "Güncel/En Son İşveren"

#: templates/applicant_dev.html:510 templates/jobs.html:69
#: templates/manage_permissions.html:117 templates/manage_permissions.html:264
#: templates/people.html:41 templates/people.html:89
#: templates/published_job_details.html:279
msgid "Status"
msgstr "Durum"

#: templates/applicant_dev.html:516
msgid "Application ID"
msgstr "Başvuru ID"

#: templates/applicant_dev.html:526
msgid "Total Experience"
msgstr "Toplam Tecrübe"

#: templates/applicant_dev.html:533
msgid "Education Level"
msgstr "Eğitim Düzeyi"

#: templates/applicant_dev.html:540
msgid "Notice Period"
msgstr "İhbar Süresi"

#: templates/applicant_dev.html:546
msgid "Last Communication Date"
msgstr "Son İletişim Tarihi"

#: templates/applicant_dev.html:551 templates/applicant_dev.html:722
msgid "No emails found."
msgstr "E-posta bulunamadı."

#: templates/applicant_dev.html:570
msgid "Uploaded on"
msgstr "Yüklenme Tarihi"

#: templates/applicant_dev.html:588
msgid "Open in New Tab"
msgstr "Yeni Sekmede Aç"

#: templates/applicant_dev.html:593
msgid "Download"
msgstr "İndir"

#: templates/applicant_dev.html:619
msgid "PDF Preview Not Available"
msgstr "PDF Önizleme Kullanılamıyor"

#: templates/applicant_dev.html:620
msgid ""
"Your browser doesn't support PDF preview. Please download the file to view "
"it."
msgstr "Tarayıcınız PDF önizlemeyi desteklemiyor. Lütfen dosyayı indirin."

#: templates/applicant_dev.html:627
msgid "Download PDF"
msgstr "CV'yi İndir"

#: templates/applicant_dev.html:644
msgid "Application Stages"
msgstr "Başvuru Aşamaları"

#: templates/applicant_dev.html:661
msgid "Started on:"
msgstr "Başlangıç:"

#: templates/applicant_dev.html:673
msgid "No stages available for this application."
msgstr "Bu aday için hiçbir durum seçilebilir değil."

#: templates/applicant_dev.html:691
msgid "Email History"
msgstr "E-posta Geçmişi"

#: templates/applicant_dev.html:701
msgid "From:"
msgstr "Gönderen:"

#: templates/applicant_dev.html:702
msgid "To:"
msgstr "Alıcı:"

#: templates/applicant_dev.html:705 templates/applicant_dev.html:1145
msgid "Subject:"
msgstr "Konu:"

#: templates/applicant_dev.html:729 templates/applicant_dev.html:775
msgid "Send Email"
msgstr "E-posta Gönder"

#: templates/applicant_dev.html:744
msgid "Subject"
msgstr "Konu"

#: templates/applicant_dev.html:754 templates/published_job_details.html:443
msgid "Email Body"
msgstr "E-posta İçeriği"

#: templates/applicant_dev.html:797
msgid "Add a comment"
msgstr "Yorum Ekle"

#: templates/applicant_dev.html:803
msgid "Add your comment here..."
msgstr "Yorumunuzu buraya girin..."

#: templates/applicant_dev.html:808
msgid "Post Comment"
msgstr "Yorumu Gönder"

#: templates/applicant_dev.html:841
msgid "No comments yet. Be the first to comment!"
msgstr "Henüz yorum yapılmadı. İlk yorumu siz yapın!"

#: templates/applicant_dev.html:876 templates/feed.html:302
msgid "Event Title"
msgstr "Etkinlik Başlığı"

#: templates/applicant_dev.html:887 templates/feed.html:312
msgid "Event Type"
msgstr "Etkinlik Türü"

#: templates/applicant_dev.html:893 templates/feed.html:318
msgid "Select an event type"
msgstr "Bir etkinlik türü seçin"

#: templates/applicant_dev.html:901 templates/feed.html:326
#: templates/manage_permissions.html:49
msgid "Recruiters"
msgstr "İşe Alım Uzmanları"

#: templates/applicant_dev.html:906 templates/feed.html:331
msgid "Select one or many recruiters"
msgstr "Bir veya birden fazla işe alım uzmanı seçin"

#: templates/applicant_dev.html:916
#: templates/emails/application_confirmation.html:26
#: templates/emails/application_status_change.html:20
#: templates/emails/general_message.html:27
#: templates/emails/meeting_invitation.html:31 templates/feed.html:384
#: templates/people.html:31 templates/people.html:88 templates/profile.html:74
#: templates/profile.html:115
msgid "Position"
msgstr "Pozisyon"

#: templates/applicant_dev.html:933 templates/feed.html:406
msgid "Candidate"
msgstr "Aday"

#: templates/applicant_dev.html:949
msgid "Date"
msgstr "Tarih"

#: templates/applicant_dev.html:958 templates/feed.html:420
msgid "Start Time"
msgstr "Başlangıç Zamanı"

#: templates/applicant_dev.html:964 templates/feed.html:425
msgid "End Time"
msgstr "Bitiş Zamanı"

#: templates/applicant_dev.html:971 templates/emails/meeting_invitation.html:78
#: templates/feed.html:431
msgid "Meeting Link"
msgstr "Toplantı Linki"

#: templates/applicant_dev.html:984 templates/feed.html:445
msgid "Generate Mirotalk Link"
msgstr "Mirotalk Bağlantısı Oluştur"

#: templates/applicant_dev.html:999 templates/feed.html:460
msgid "Inform invitees by E-mail"
msgstr "Davetlileri E-posta ile bilgilendir"

#: templates/applicant_dev.html:1004 templates/feed.html:465
msgid "Color"
msgstr "Renk"

#: templates/applicant_dev.html:1006 templates/feed.html:467
msgid "Blue"
msgstr "Mavi"

#: templates/applicant_dev.html:1007 templates/feed.html:468
msgid "Light Blue"
msgstr "Açık Mavi"

#: templates/applicant_dev.html:1008 templates/feed.html:469
msgid "Purple"
msgstr "Mor"

#: templates/applicant_dev.html:1009 templates/feed.html:470
msgid "Pink"
msgstr "Pembe"

#: templates/applicant_dev.html:1016 templates/applicant_dev.html:1173
#: templates/create_job_template.html:205 templates/feed.html:476
#: templates/job_details.html:134 templates/manage_permissions.html:413
#: templates/manage_permissions.html:463 templates/profile.html:125
#: templates/profile.html:152 templates/profile.html:184
#: templates/published_job_details.html:380
#: templates/published_job_details.html:465
#: templates/published_job_details.html:686
msgid "Cancel"
msgstr "İptal"

#: templates/applicant_dev.html:1019 templates/feed.html:479
msgid "Save Event"
msgstr "Etkinliği Kaydet"

#: templates/applicant_dev.html:1078
msgid "Change Application Status"
msgstr "Başvuru Durumunu Değiştir"

#: templates/applicant_dev.html:1096
msgid "New Status"
msgstr "Yeni Durum"

#: templates/applicant_dev.html:1098
#: templates/emails/application_confirmation.html:37
msgid "New"
msgstr "Yeni"

#: templates/applicant_dev.html:1099
msgid "Review #1"
msgstr "Görüşme #1"

#: templates/applicant_dev.html:1100
msgid "Review #2"
msgstr "Görüşme #2"

#: templates/applicant_dev.html:1101
msgid "Review #3"
msgstr "Görüşme #3"

#: templates/applicant_dev.html:1102
msgid "Review #4"
msgstr "Görüşme #4"

#: templates/applicant_dev.html:1103
msgid "Review #5"
msgstr "Görüşme #5"

#: templates/applicant_dev.html:1104
msgid "Ready for Decision"
msgstr "Karar için hazır"

#: templates/applicant_dev.html:1105
msgid "Eliminated"
msgstr "Elimine edildi"

#: templates/applicant_dev.html:1106
msgid "Offer Made"
msgstr "Teklif verildi"

#: templates/applicant_dev.html:1107
msgid "Candidate Accepted"
msgstr "Aday kabul etti"

#: templates/applicant_dev.html:1108
msgid "Candidate Rejected"
msgstr "Aday reddetti"

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "Internal Notes"
msgstr "Dahili Notlar"

#: templates/applicant_dev.html:1112 templates/published_job_details.html:432
msgid "(visible only to recruiters)"
msgstr "(sadece işe alım uzmanları tarafından görülebilir)"

#: templates/applicant_dev.html:1126
msgid "Notify candidate about this status change via email"
msgstr "Bu Durum gelişmesi için adayı e-posta ile bilgilendir"

#: templates/applicant_dev.html:1132
msgid "Email Message"
msgstr "E-posta Mesajı"

#: templates/applicant_dev.html:1132
msgid "(will be included in the email to the candidate)"
msgstr "(adaya gönderilen e-postaya dahil edilecek)"

#: templates/applicant_dev.html:1143
msgid "Email Preview"
msgstr "E-posta Önizleme"

#: templates/applicant_dev.html:1145
msgid "Your application status has been updated"
msgstr "Başvuru durumunuz güncellendi"

#: templates/applicant_dev.html:1175
msgid "Save Change"
msgstr "Değişikliği Kaydet"

#: templates/careers_page.html:10
msgid "Career Page Setup"
msgstr "Kariyer Sayfası Kurulumu"

#: templates/careers_page.html:11
msgid "Choose the integration method that best suits your needs"
msgstr "Sizinkine en uygun entegrasyon yöntemini seçin"

#: templates/careers_page.html:23
msgid "RSS Feed Integration"
msgstr "RSS Beslemesi Entegrasyonu"

#: templates/careers_page.html:24
msgid ""
"Already have your own careers page? Get our RSS feed to sync job listings"
msgstr ""
"Kendi kariyer sayfanız var mı? İş ilanlarınızı senkronize etmek için RSS "
"beslememizi alın"

#: templates/careers_page.html:26
msgid "Quick Setup"
msgstr "Hızlı Kurulum"

#: templates/careers_page.html:27
msgid "Auto Sync"
msgstr "Otomatik Senkronizasyon"

#: templates/careers_page.html:32
msgid "Get RSS Feed"
msgstr "RSS Feed'i Al"

#: templates/careers_page.html:40
msgid "Recommended"
msgstr "Tavsiye Edilen"

#: templates/careers_page.html:45
msgid "Full HTML Page"
msgstr "Tam HTML Sayfası"

#: templates/careers_page.html:46
msgid "Let us manage your entire careers page with our professional template"
msgstr "Kariyer sayfanızı profesyonel şablonumuzla yönetelim"

#: templates/careers_page.html:48
msgid "Professional"
msgstr "Profesyonel"

#: templates/careers_page.html:49
msgid "Customizable"
msgstr "Ayarlanabilir"

#: templates/careers_page.html:54
msgid "Create Page"
msgstr "Sayfa Oluştur"

#: templates/careers_page.html:67
msgid "Workloupe Platform"
msgstr "Workloupe Platformu"

#: templates/careers_page.html:68
msgid "Use our platform as your company's career page"
msgstr "Platformunuzu şirketinizin kariyer sayfası olarak kullanın"

#: templates/careers_page.html:70
msgid "Hosted"
msgstr "Host edilen"

#: templates/careers_page.html:71
msgid "Full Featured"
msgstr "Tam Özellikli"

#: templates/careers_page.html:76
msgid "Setup Platform"
msgstr "Platformu Kur"

#: templates/careers_page.html:91
msgid "Your RSS Feed URL"
msgstr "RSS Feed URL'niz"

#: templates/careers_page.html:98
msgid ""
"Use this RSS feed URL to automatically sync your job listings with your "
"existing careers page."
msgstr ""
"Bu RSS feed URL'ini, iş ilanlarınızı mevcut kariyer sayfanızla otomatik "
"olarak senkronize etmek için kullanın."

#: templates/careers_page.html:101
msgid "RSS Feed URL"
msgstr "RSS Feed URL'i"

#: templates/careers_page.html:106 templates/published_job_details.html:516
#: templates/published_job_details.html:559
#: templates/published_job_details.html:585
#: templates/workloupe_platform.html:311
msgid "Copy"
msgstr "Kopyala"

#: templates/careers_page.html:111 templates/create_careers_widget.html:182
msgid "Integration Instructions:"
msgstr "Entegrasyon Talimatları:"

#: templates/careers_page.html:113
msgid "Copy the RSS feed URL above"
msgstr "Yukarıdaki RSS feed URL'ini kopyala"

#: templates/careers_page.html:114
msgid "Add it to your website's RSS feed reader or job board integration"
msgstr ""
"Web sitenizin RSS feed okuyucusuna veya iş panosu entegrasyonuna ekleyin"

#: templates/careers_page.html:115
msgid "Your job listings will automatically sync"
msgstr "İş ilanlarınız otomatik olarak senkronize edilecek"

#: templates/careers_page.html:286 templates/create_careers_widget.html:716
#: templates/wordpress_integration.html:775
#: templates/workloupe_platform.html:1142
msgid "Copied!"
msgstr "Kopyalandı!"

#: templates/careers_page.html:297 templates/create_careers_widget.html:726
#: templates/wordpress_integration.html:785
msgid "Failed to copy. Please copy manually."
msgstr "Kopyalama başarısız oldu. Lütfen elle kopyalayın."

#: templates/create_careers_widget.html:13
msgid "Widget Builder"
msgstr "Araç Oluşturucu"

#: templates/create_careers_widget.html:14
msgid "Customize your careers widget"
msgstr "Kariyer araçlarınızı özelleştirin"

#: templates/create_careers_widget.html:22
#: templates/wordpress_integration.html:45
msgid "Company Branding"
msgstr "Şirket Markalama"

#: templates/create_careers_widget.html:26
#: templates/wordpress_integration.html:49 templates/workloupe_platform.html:34
msgid "Company Name"
msgstr "Şirket Adı"

#: templates/create_careers_widget.html:27
#: templates/wordpress_integration.html:50
msgid "Enter company name"
msgstr "Şirket adını girin"

#: templates/create_careers_widget.html:31
#: templates/wordpress_integration.html:54
msgid "Tagline"
msgstr "Slogan"

#: templates/create_careers_widget.html:32
#: templates/wordpress_integration.html:55
msgid "Enter company tagline"
msgstr "Şirket sloganını girin"

#: templates/create_careers_widget.html:36
#: templates/workloupe_platform.html:175
msgid "Company Logo"
msgstr "Şirket Logosu"

#: templates/create_careers_widget.html:38
msgid "Recommended: 200x80px, PNG or JPG"
msgstr "Tavsiye Edilen: 200x80px, PNG veya JPG"

#: templates/create_careers_widget.html:46
msgid "Design & Colors"
msgstr "Tasarım & Renkler"

#: templates/create_careers_widget.html:50
#: templates/wordpress_integration.html:67
msgid "Primary Color"
msgstr "Ana Renk"

#: templates/create_careers_widget.html:55
msgid "Background Color"
msgstr "Arka Plan Rengi"

#: templates/create_careers_widget.html:60
msgid "Text Color"
msgstr "Metin Rengi"

#: templates/create_careers_widget.html:65
msgid "Widget Style"
msgstr "Araç Stili"

#: templates/create_careers_widget.html:67
#: templates/wordpress_integration.html:75
msgid "Modern"
msgstr "Modern"

#: templates/create_careers_widget.html:68
#: templates/wordpress_integration.html:76
msgid "Classic"
msgstr "Classic"

#: templates/create_careers_widget.html:69
#: templates/wordpress_integration.html:77
msgid "Minimal"
msgstr "Minimal"

#: templates/create_careers_widget.html:78
#: templates/wordpress_integration.html:93
msgid "Content Settings"
msgstr "İçerik Ayarları"

#: templates/create_careers_widget.html:82
msgid "Max Jobs to Display"
msgstr "Maximum Gösterilecek İlan"

#: templates/create_careers_widget.html:84
#: templates/create_careers_widget.html:85
#: templates/create_careers_widget.html:86 templates/jobs.html:186
#: templates/wordpress_integration.html:99
#: templates/wordpress_integration.html:100
#: templates/wordpress_integration.html:101
msgid "jobs"
msgstr "ilan"

#: templates/create_careers_widget.html:87
#: templates/wordpress_integration.html:102
msgid "All jobs"
msgstr "Tüm ilanlar"

#: templates/create_careers_widget.html:94
msgid "Show Salary Information"
msgstr "Maaş Bilgilerini Göster"

#: templates/create_careers_widget.html:101
msgid "Show Job Location"
msgstr "İş Konumunu Göster"

#: templates/create_careers_widget.html:108
msgid "Show Posted Date"
msgstr "Yayınlanma Tarihini Göster"

#: templates/create_careers_widget.html:117
msgid "Generate Widget Code"
msgstr "Araç Kodunu Oluştur"

#: templates/create_careers_widget.html:126
msgid "Live Preview"
msgstr "Canlı Önizleme"

#: templates/create_careers_widget.html:160
msgid "Your Widget Code"
msgstr "Sizin Araç Kodunuz"

#: templates/create_careers_widget.html:167
msgid ""
"Copy this code and paste it into your website where you want the careers "
"widget to appear."
msgstr ""
"Bu kodu kopyalayın ve kariyer araçlarının görüntüleneceği web sitenizin "
"HTML'sine yapıştırın."

#: templates/create_careers_widget.html:172
msgid "HTML Widget Code"
msgstr "HTML Araç Kodu"

#: templates/create_careers_widget.html:175
#: templates/wordpress_integration.html:246
msgid "Copy Code"
msgstr "Kodu Kopyala"

#: templates/create_careers_widget.html:184
msgid "Copy the HTML code above"
msgstr "Yukarıdaki HTML kodunu kopyala"

#: templates/create_careers_widget.html:185
msgid "Paste it into your website's HTML where you want the widget to appear"
msgstr "Araçın görüntüleneceği yerdeki HTML'in içine yapıştırın"

#: templates/create_careers_widget.html:186
msgid "The widget will automatically load your latest job postings"
msgstr "Araç, güncel iş ilanlarınızı otomatik olarak yükleyecektir"

#: templates/create_careers_widget.html:187
msgid "The widget is responsive and will adapt to your website's layout"
msgstr "Araç, web sitenizin düzenine göre uyarlanacaktır"

#: templates/create_careers_widget.html:192
#: templates/wordpress_integration.html:253
#: templates/workloupe_platform.html:338
msgid "Close"
msgstr "Kapat"

#: templates/create_careers_widget.html:195
msgid "Download as HTML File"
msgstr "HTML Dosyası Olarak İndir"

#: templates/create_careers_widget.html:802
msgid ""
"Widget package downloaded! Extract and follow the README instructions for "
"integration."
msgstr "Araç paketi indirildi! Entegrasyon için README talimatlarını izleyin."

#: templates/create_careers_widget.html:823
msgid "Widget file downloaded! Copy the code and paste it into your website."
msgstr "Araç dosyası indirildi! Kodu kopyalayın ve web sitenize yapıştırın."

#: templates/create_job.html:3
msgid "Create Job Position"
msgstr "İş Pozisyonu Oluştur"

#: templates/create_job.html:8
msgid "Basic Information"
msgstr "Temel Bilgiler"

#: templates/create_job.html:10 templates/job_details.html:1072
#: templates/job_preview_publish.html:590
msgid "Role Title"
msgstr "Rol Başlığı"

#: templates/create_job.html:14
msgid "e.g. Senior Software Engineer"
msgstr "örn. Kıdemli Yazılım Mühendisi"

#: templates/create_job.html:18 templates/job_details.html:1076
#: templates/job_preview_publish.html:594
msgid "Office Location"
msgstr "Ofis Konumu"

#: templates/create_job.html:21
msgid "Select office location"
msgstr "Ofis konumu seçin"

#: templates/create_job.html:31
msgid "No office locations found. Please"
msgstr "Ofis konumu bulunamadı. Lütfen"

#: templates/create_job.html:32
msgid "add office locations"
msgstr "ofis konumları ekleyin"

#: templates/create_job.html:32 templates/create_job.html:53
#: templates/create_job.html:74 templates/create_job.html:95
msgid "in your preferences first."
msgstr "önce tercihlerinizde."

#: templates/create_job.html:36
msgid "No locations available"
msgstr "Mevcut konum yok"

#: templates/create_job.html:41 templates/job_details.html:1080
#: templates/job_preview_publish.html:598
msgid "Work Schedule"
msgstr "Çalışma Programı"

#: templates/create_job.html:44 templates/create_job.html:65
msgid "Select an option"
msgstr "Bir seçenek seçin"

#: templates/create_job.html:52
msgid "No work schedules found. Please"
msgstr "Çalışma programı bulunamadı. Lütfen"

#: templates/create_job.html:53
msgid "add work schedules"
msgstr "çalışma programları ekleyin"

#: templates/create_job.html:57
msgid "No work schedules available"
msgstr "Mevcut çalışma programı yok"

#: templates/create_job.html:62 templates/job_details.html:1084
#: templates/job_preview_publish.html:604
msgid "Office Schedule"
msgstr "Ofis Programı"

#: templates/create_job.html:73
msgid "No office schedules found. Please"
msgstr "Ofis programı bulunamadı. Lütfen"

#: templates/create_job.html:74
msgid "add office schedules"
msgstr "ofis programları ekleyin"

#: templates/create_job.html:78
msgid "No office schedules available"
msgstr "Mevcut ofis programı yok"

#: templates/create_job.html:83 templates/job_details.html:1088
#: templates/job_preview_publish.html:610 templates/jobs.html:59
#: templates/profile.html:119
msgid "Department"
msgstr "Departman"

#: templates/create_job.html:86
msgid "Select a department"
msgstr "Departman seçin"

#: templates/create_job.html:94
msgid "No departments found. Please"
msgstr "Departman bulunamadı. Lütfen"

#: templates/create_job.html:95
msgid "add departments"
msgstr "departman ekleyin"

#: templates/create_job.html:99
msgid "No departments available"
msgstr "Mevcut departman yok"

#: templates/create_job.html:107
msgid "Skills Requirements"
msgstr "Yetenek Gereksinimleri"

#: templates/create_job.html:109
msgid "Skill"
msgstr "Yetenek"

#: templates/create_job.html:114
msgid "e.g. JavaScript"
msgstr "örn. JavaScript"

#: templates/create_job.html:116 templates/create_job.html:190
msgid "Add"
msgstr "Ekle"

#: templates/create_job.html:119
msgid "Choose Skills"
msgstr "Yetenekleri Seçin"

#: templates/create_job.html:133
msgid "Selected Skills"
msgstr "Seçilen Yetenekler"

#: templates/create_job.html:136
msgid "No skills selected yet"
msgstr "Henüz yetenek seçilmedi"

#: templates/create_job.html:146
msgid "Salary Details (Optional)"
msgstr "Maaş Detayları (İsteğe Bağlı)"

#: templates/create_job.html:149
msgid "Minimum Salary"
msgstr "Minimum Maaş"

#: templates/create_job.html:153
msgid "Enter minimum salary"
msgstr "Minimum maaş girin"

#: templates/create_job.html:157
msgid "Maximum Salary"
msgstr "Maksimum Maaş"

#: templates/create_job.html:161
msgid "Enter maximum salary"
msgstr "Maksimum maaş girin"

#: templates/create_job.html:165
msgid "Currency"
msgstr "Para Birimi"

#: templates/create_job.html:167
msgid "Select currency"
msgstr "Para birimi seçin"

#: templates/create_job.html:183
msgid "Benefits and Highlights (Optional)"
msgstr "Yan Haklar ve Öne Çıkanlar (İsteğe Bağlı)"

#: templates/create_job.html:188
msgid "e.g. Yearly Bonuses"
msgstr "örn. Yıllık Bonuslar"

#: templates/create_job.html:193
msgid "Choose Benefits"
msgstr "Yan Hakları Seçin"

#: templates/create_job.html:196
msgid "Dental Coverage"
msgstr "Diş Sigortası"

#: templates/create_job.html:199
msgid "Private Health Coverage"
msgstr "Özel Sağlık Sigortası"

#: templates/create_job.html:202
msgid "Gym membership"
msgstr "Spor Salonu Üyeliği"

#: templates/create_job.html:205
msgid "Sign-in Bonus"
msgstr "Giriş Bonusu"

#: templates/create_job.html:208
msgid "Relocation Package"
msgstr "Taşınma Paketi"

#: templates/create_job.html:211
msgid "Company Vehicle"
msgstr "Şirket Aracı"

#: templates/create_job.html:213
msgid "Food Card"
msgstr "Yemek Kartı"

#: templates/create_job.html:215
msgid "Snacks & Coffee"
msgstr "Atıştırmalık ve Kahve"

#: templates/create_job.html:218
msgid "Pet Friendly Office"
msgstr "Evcil Hayvan Dostu Ofis"

#: templates/create_job.html:222
msgid "Selected Benefits & Highlights"
msgstr "Seçilen Yan Haklar ve Öne Çıkanlar"

#: templates/create_job.html:225
msgid "No benefits or highlights selected yet"
msgstr "Henüz yan hak veya öne çıkan seçilmedi"

#: templates/create_job.html:232 templates/job_details.html:107
#: templates/job_preview_publish.html:159
msgid "Discard"
msgstr "İptal Et"

#: templates/create_job.html:233
msgid "Next"
msgstr "İleri"

#: templates/create_job_template.html:13 templates/create_job_template.html:19
#: templates/settings.html:41
msgid "Templates"
msgstr "Şablonlar"

#: templates/create_job_template.html:14
msgid "Create and manage reusable job description templates"
msgstr "Yeniden kullanılabilir iş tanımı şablonları oluşturun ve yönetin"

#: templates/create_job_template.html:17 templates/job_preferences.html:15
#: templates/manage_permissions.html:15 templates/navbar.html:104
#: templates/settings.html:9
msgid "Settings"
msgstr "Ayarlar"

#: templates/create_job_template.html:31
msgid "Total Templates"
msgstr "Toplam Şablon"

#: templates/create_job_template.html:41
msgid "Created This Month"
msgstr "Bu Ay Oluşturulan"

#: templates/create_job_template.html:51
msgid "Jobs Created from Templates"
msgstr "Şablonlardan Oluşturulan İşler"

#: templates/create_job_template.html:61
msgid "Time Saved Using Templates"
msgstr "Şablonlar Kullanarak Kazanılan Zaman"

#: templates/create_job_template.html:71
msgid "My Templates"
msgstr "Şablonlarım"

#: templates/create_job_template.html:74 templates/create_job_template.html:105
#: templates/create_job_template.html:118
msgid "New Template"
msgstr "Yeni Şablon"

#: templates/create_job_template.html:80
msgid "Search templates..."
msgstr "Şablon ara..."

#: templates/create_job_template.html:91
msgid "Updated"
msgstr "Güncellendi"

#: templates/create_job_template.html:94
msgid "Used 1 time"
msgstr "1 kez kullanıldı"

#: templates/create_job_template.html:96
msgid "Used"
msgstr "Kullanıldı"

#: templates/create_job_template.html:96
msgid "times"
msgstr "kez"

#: templates/create_job_template.html:107
msgid "Not saved yet"
msgstr "Henüz kaydedilmedi"

#: templates/create_job_template.html:108
msgid "Not used yet"
msgstr "Henüz kullanılmadı"

#: templates/create_job_template.html:118
msgid "Enter template title"
msgstr "Şablon başlığını girin"

#: templates/create_job_template.html:121
#: templates/create_job_template.html:196
msgid "Delete Template"
msgstr "Şablonu Sil"

#: templates/create_job_template.html:126 templates/job_details.html:135
msgid "Save Template"
msgstr "Şablonu Kaydet"

#: templates/create_job_template.html:136
msgid "Heading 1"
msgstr "Başlık 1"

#: templates/create_job_template.html:137
msgid "Heading 2"
msgstr "Başlık 2"

#: templates/create_job_template.html:138
msgid "Heading 3"
msgstr "Başlık 3"

#: templates/create_job_template.html:139
msgid "Paragraph"
msgstr "Paragraf"

#: templates/create_job_template.html:143
msgid "Bold"
msgstr "Kalın"

#: templates/create_job_template.html:144
msgid "Italic"
msgstr "İtalik"

#: templates/create_job_template.html:145
msgid "Underline"
msgstr "Altı Çizili"

#: templates/create_job_template.html:149
msgid "Bullet List"
msgstr "Madde İşaretli Liste"

#: templates/create_job_template.html:150
msgid "Numbered List"
msgstr "Numaralı Liste"

#: templates/create_job_template.html:179
msgid "Enter your template content here..."
msgstr "Şablon içeriğinizi buraya girin..."

#: templates/create_job_template.html:185 templates/job_details.html:100
msgid "characters"
msgstr "karakter"

#: templates/create_job_template.html:201
msgid "Are you sure you want to delete the"
msgstr "Silmek istediğinize emin misiniz?"

#: templates/create_job_template.html:201
msgid "template? This action cannot be undone."
msgstr "Bu işlem geri alınamaz."

#: templates/create_job_template.html:206 templates/job_preferences.html:70
msgid "Delete"
msgstr "Sil"

# Application Confirmation Email
#: templates/emails/application_confirmation.html:4
msgid "Application Received"
msgstr "Başvuru Alındı"

#: templates/emails/application_confirmation.html:6
msgid "Application Confirmation"
msgstr "Başvuru Onayı"

#: templates/emails/application_confirmation.html:11
msgid "Thank you for your application!"
msgstr "Başvurunuz için teşekkür ederiz!"

#: templates/emails/application_confirmation.html:14
#, python-format
msgid ""
"\n"
"        We have successfully received your application for the position of "
"<strong>%(job_title)s</strong>.\n"
"        "
msgstr ""
"\n"
"        <strong>%(job_title)s</strong> pozisyonu için başvurunuzu başarıyla "
"aldık.\n"
"        "

#: templates/emails/application_confirmation.html:22
msgid "Application Summary"
msgstr "Başvuru Özeti"

# msgid "Position"
# msgstr "Pozisyon"
#: templates/emails/application_confirmation.html:34
#: templates/emails/application_status_change.html:24
msgid "Application Status"
msgstr "Başvuru Durumu"

#: templates/emails/application_confirmation.html:43
msgid "Application Source"
msgstr "Başvuru Kaynağı"

#: templates/emails/application_confirmation.html:51
msgid "What Happens Next?"
msgstr "Sırada Ne Var?"

#: templates/emails/application_confirmation.html:53
msgid "Our recruitment team will review your application"
msgstr "İşe alım ekibimiz başvurunuzu inceleyecek"

#: templates/emails/application_confirmation.html:54
msgid ""
"If your profile matches our requirements, we will contact you for the next "
"steps"
msgstr ""
"Profiliniz gereksinimlerimizle eşleşirse, sonraki adımlar için sizinle "
"iletişime geçeceğiz"

#: templates/emails/application_confirmation.html:55
msgid ""
"You will receive email updates about any changes to your application status"
msgstr ""
"Başvuru durumunuzdaki değişiklikler hakkında e-posta güncellemeleri "
"alacaksınız"

#: templates/emails/application_confirmation.html:56
msgid "The review process typically takes 5-10 business days"
msgstr "İnceleme süreci genellikle 5-10 iş günü sürer"

#: templates/emails/application_confirmation.html:61
msgid "Stay Updated"
msgstr "Güncel Kalın"

#: templates/emails/application_confirmation.html:63
msgid ""
"We will keep you informed throughout the selection process via email. Please "
"ensure that emails from our domain are not filtered as spam."
msgstr ""
"Seçim süreci boyunca sizi e-posta yoluyla bilgilendireceğiz. Lütfen "
"domain'imizden gelen e-postaların spam olarak filtrelenmediğinden emin olun."

#: templates/emails/application_confirmation.html:66
msgid ""
"If you have any questions about your application or the position, please "
"don't hesitate to contact us using the reference ID provided at the bottom "
"of this email."
msgstr ""
"Başvurunuz veya pozisyon hakkında herhangi bir sorunuz varsa, bu e-postanın "
"altında verilen referans numarasını kullanarak bizimle iletişime geçmekten "
"çekinmeyin."

#: templates/emails/application_confirmation.html:71
msgid "About Our Company"
msgstr "Şirketimiz Hakkında"

#: templates/emails/application_confirmation.html:73
msgid ""
"We appreciate your interest in joining our team. We are committed to "
"creating an inclusive and diverse workplace where everyone can thrive."
msgstr ""
"Ekibimize katılma ilginizi takdir ediyoruz. Herkesin gelişebileceği "
"kapsayıcı ve çeşitli bir çalışma ortamı yaratmaya kararlıyız."

#: templates/emails/application_confirmation.html:79
msgid ""
"Thank you for considering us as your next career opportunity. We look "
"forward to potentially working with you!"
msgstr ""
"Bizi bir sonraki kariyer fırsatınız olarak değerlendirdiğiniz için teşekkür "
"ederiz. Sizinle çalışma potansiyelini dört gözle bekliyoruz!"

# Application Status Update Email
#: templates/emails/application_status_change.html:4
#: templates/emails/application_status_change.html:6
msgid "Application Status Update"
msgstr "Başvuru Durumu Güncelleme"

#: templates/emails/application_status_change.html:11
#, python-format
msgid ""
"\n"
"        We would like to inform you that your application for the position "
"of <strong>%(job_title)s</strong> has been updated.\n"
"        "
msgstr ""
"\n"
"        <strong>%(job_title)s</strong> pozisyonu için başvurunuzun "
"güncellendiğini bildirmek isteriz.\n"
"        "

# msgid "Application Date"
# msgstr "Başvuru Tarihi"
#: templates/emails/application_status_change.html:37
msgid "Last Updated"
msgstr "Son Güncelleme"

#: templates/emails/application_status_change.html:46
msgid "Additional Information"
msgstr "Ek Bilgiler"

#: templates/emails/application_status_change.html:54
msgid "Congratulations! We are excited to welcome you to our team."
msgstr "Tebrikler! Sizi ekibimizde karşılamaktan heyecan duyuyoruz."

#: templates/emails/application_status_change.html:57
msgid ""
"Our HR team will be in touch with you shortly regarding the next steps, "
"including onboarding information and your start date."
msgstr ""
"İK ekibimiz, işe başlama bilgileri ve başlangıç tarihiniz dahil olmak üzere "
"sonraki adımlar hakkında kısa süre içinde sizinle iletişime geçecektir."

#: templates/emails/application_status_change.html:63
msgid ""
"Your application is currently under review. We will keep you updated on any "
"developments."
msgstr ""
"Başvurunuz şu anda inceleme altındadır. Gelişmeler hakkında sizi "
"bilgilendirmeye devam edeceğiz."

#: templates/emails/application_status_change.html:67
msgid "Your application has reached the final stage of our selection process."
msgstr "Başvurunuz seçim sürecimizin son aşamasına ulaştı."

#: templates/emails/application_status_change.html:74
msgid ""
"While we were impressed with your qualifications, we have decided to move "
"forward with other candidates for this particular position."
msgstr ""
"Niteliklerinizden etkilenmiş olmakla birlikte, bu pozisyon için diğer "
"adaylarla devam etmeye karar verdik."

#: templates/emails/application_status_change.html:77
msgid ""
"We encourage you to apply for future opportunities that match your skills "
"and experience. We will keep your profile in our talent database for "
"consideration in upcoming roles."
msgstr ""
"Becerileriniz ve deneyiminizle uyumlu gelecekteki fırsatlar için "
"başvurmanızı teşvik ediyoruz. Profilinizi gelecekteki roller için "
"değerlendirmek üzere yetenek veri tabanımızda tutacağız."

#: templates/emails/application_status_change.html:83
msgid ""
"We will continue to review your application and keep you informed of any "
"updates throughout the process."
msgstr ""
"Başvurunuzu incelemeye devam edeceğiz ve süreç boyunca güncellemeler "
"hakkında sizi bilgilendireceğiz."

#: templates/emails/application_status_change.html:90
msgid ""
"Thank you for your interest in joining our team. We appreciate the time you "
"have invested in the application process."
msgstr ""
"Ekibimize katılma ilginiz için teşekkür ederiz. Başvuru sürecine ayırdığınız "
"zamanı takdir ediyoruz."

# Email-related translations
#: templates/emails/base_email.html:219 templates/emails/base_email.html:236
msgid "Recruitment Team"
msgstr "İşe Alım Ekibi"

#: templates/emails/base_email.html:226 templates/emails/team_invitation.html:9
msgid "Dear"
msgstr "Sayın"

#: templates/emails/base_email.html:237
msgid "Human Resources Department"
msgstr "İnsan Kaynakları Departmanı"

#: templates/emails/base_email.html:244
msgid "If you have any questions, please don't hesitate to contact us."
msgstr "Herhangi bir sorunuz varsa, bizimle iletişime geçmekten çekinmeyin."

#: templates/emails/base_email.html:248
msgid "Reference ID:"
msgstr "Referans Numarası:"

#: templates/emails/base_email.html:252
msgid "This email was sent by"
msgstr "Bu e-posta tarafından gönderildi"

# General Message Email
#: templates/emails/general_message.html:6
msgid "Message from Recruitment Team"
msgstr "İşe Alım Ekibinden Mesaj"

#: templates/emails/general_message.html:12
#, fuzzy, python-format
#| msgid ""
#| "\n"
#| "        We have successfully received your application for the position "
#| "of <strong>%(job_title)s</strong>.\n"
#| "        "
msgid ""
"\n"
"        This message is regarding your application for the position of "
"<strong>%(job_title)s</strong>.\n"
"        "
msgstr ""
"\n"
"        <strong>%(job_title)s</strong> pozisyonu için başvurunuzu başarıyla "
"aldık.\n"
"        "

#: templates/emails/general_message.html:36
msgid "Current Status"
msgstr "Mevcut Durum"

#: templates/emails/general_message.html:50
msgid ""
"Thank you for your continued interest in our company. We appreciate your "
"patience throughout the application process."
msgstr ""
"Şirketimize olan sürekli ilginiz için teşekkür ederiz. Başvuru süreci "
"boyunca gösterdiğiniz sabır için minnettarız."

# Meeting Invitation Email
#: templates/emails/meeting_invitation.html:4
msgid "Meeting Invitation"
msgstr "Toplantı Daveti"

#: templates/emails/meeting_invitation.html:6
msgid "Interview Invitation"
msgstr "Mülakat Daveti"

#: templates/emails/meeting_invitation.html:11
msgid "Great news! We would like to invite you for an interview."
msgstr "Harika haber! Sizi mülakat için davet etmek istiyoruz."

#: templates/emails/meeting_invitation.html:14
#, python-format
msgid ""
"\n"
"        We are pleased to invite you to an interview for the position of "
"<strong>%(job_title)s</strong>. \n"
"        We were impressed with your application and would like to learn more "
"about you.\n"
"        "
msgstr ""
"\n"
"        <strong>%(job_title)s</strong> pozisyonu için sizi mülakata davet "
"etmekten memnuniyet duyuyoruz. \n"
"        Başvurunuzdan etkilendik ve sizi daha yakından tanımak istiyoruz.\n"
"        "

#: templates/emails/meeting_invitation.html:23
msgid "Meeting Details"
msgstr "Toplantı Detayları"

#: templates/emails/meeting_invitation.html:27
msgid "Meeting Title"
msgstr "Toplantı Başlığı"

#: templates/emails/meeting_invitation.html:35
msgid "Interview Type"
msgstr "Mülakat Türü"

#: templates/emails/meeting_invitation.html:38
msgid "Video Call"
msgstr "Video görüşmesi"

#: templates/emails/meeting_invitation.html:40
msgid "Phone Call"
msgstr "Telefon görüşmesi"

# msgid "Video Call"
# msgstr "Video Görüşme"
# msgid "Phone Call"
# msgstr "Telefon Görüşmesi"
#: templates/emails/meeting_invitation.html:42
msgid "In-Person Meeting"
msgstr "Yüz Yüze Toplantı"

#: templates/emails/meeting_invitation.html:49
msgid "Date & Time"
msgstr "Tarih ve Saat"

#: templates/emails/meeting_invitation.html:59
msgid "Duration"
msgstr "Süre"

#: templates/emails/meeting_invitation.html:68
msgid "Interviewer(s)"
msgstr "Mülakatçı(lar)"

# msgid "Meeting Link"
# msgstr "Toplantı Bağlantısı"
#: templates/emails/meeting_invitation.html:81
msgid "Join Meeting"
msgstr "Toplantıya Katıl"

#: templates/emails/meeting_invitation.html:92
msgid "Video Call Instructions"
msgstr "Video Görüşme Talimatları"

#: templates/emails/meeting_invitation.html:94
msgid "Please test your camera and microphone before the meeting"
msgstr "Lütfen toplantıdan önce kameranızı ve mikrofonunuzu test edin"

#: templates/emails/meeting_invitation.html:95
msgid "Ensure you have a stable internet connection"
msgstr "Stabil bir internet bağlantınız olduğundan emin olun"

#: templates/emails/meeting_invitation.html:96
msgid "Find a quiet, well-lit space for the interview"
msgstr "Mülakat için sessiz, iyi aydınlatılmış bir yer bulun"

#: templates/emails/meeting_invitation.html:97
msgid "Click the meeting link 5 minutes before the scheduled time"
msgstr "Planlanan saatten 5 dakika önce toplantı bağlantısına tıklayın"

#: templates/emails/meeting_invitation.html:102
msgid "Phone Call Instructions"
msgstr "Telefon Görüşmesi Talimatları"

#: templates/emails/meeting_invitation.html:104
msgid "Please ensure you're in a quiet location"
msgstr "Lütfen sessiz bir yerde olduğunuzdan emin olun"

#: templates/emails/meeting_invitation.html:105
msgid "Have a good phone signal or use a landline if possible"
msgstr "İyi bir telefon sinyaliniz olsun veya mümkünse sabit hat kullanın"

#: templates/emails/meeting_invitation.html:106
msgid "We will call you at the scheduled time"
msgstr "Planlanan saatte sizi arayacağız"

#: templates/emails/meeting_invitation.html:107
msgid "Have your resume and any questions ready"
msgstr "Özgeçmişinizi ve sorularınızı hazır bulundurun"

#: templates/emails/meeting_invitation.html:112
msgid "In-Person Meeting Instructions"
msgstr "Yüz Yüze Toplantı Talimatları"

#: templates/emails/meeting_invitation.html:114
msgid "Please arrive 10 minutes early"
msgstr "Lütfen 10 dakika erken gelin"

#: templates/emails/meeting_invitation.html:115
msgid "Bring a printed copy of your resume"
msgstr "Özgeçmişinizin basılı bir kopyasını getirin"

#: templates/emails/meeting_invitation.html:116
msgid "Dress professionally"
msgstr "Profesyonel giyinin"

#: templates/emails/meeting_invitation.html:117
msgid "Ask for directions at the reception if needed"
msgstr "Gerekirse resepsiyondan yol tarifi isteyin"

#: templates/emails/meeting_invitation.html:123
msgid "What to Expect"
msgstr "Ne Beklemeli"

#: templates/emails/meeting_invitation.html:125
msgid ""
"During the interview, we will discuss your background, experience, and how "
"you can contribute to our team. This is also a great opportunity for you to "
"ask questions about the role and our company culture."
msgstr ""
"Mülakat sırasında geçmişinizi, deneyiminizi ve ekibimize nasıl katkıda "
"bulunabileceğinizi tartışacağız. Bu aynı zamanda rol ve şirket kültürümüz "
"hakkında sorular sormanız için harika bir fırsattır."

#: templates/emails/meeting_invitation.html:130
msgid "Need to Reschedule?"
msgstr "Yeniden Planlamaya İhtiyacınız Var mı?"

#: templates/emails/meeting_invitation.html:132
msgid ""
"If you need to reschedule this interview, please contact us as soon as "
"possible. When reaching out, please include the reference ID mentioned at "
"the bottom of this email."
msgstr ""
"Bu mülakatı yeniden planlamanız gerekiyorsa, lütfen en kısa sürede bizimle "
"iletişime geçin. İletişime geçerken, lütfen bu e-postanın altında belirtilen "
"referans numarasını ekleyin."

#: templates/emails/meeting_invitation.html:138
msgid ""
"We look forward to meeting with you and learning more about your "
"qualifications!"
msgstr ""
"Sizinle tanışmayı ve nitelikleriniz hakkında daha fazla bilgi edinmeyi dört "
"gözle bekliyoruz!"

# Team Invitation Email
#: templates/emails/team_invitation.html:4
msgid "Team Invitation"
msgstr "Takım Daveti"

#: templates/emails/team_invitation.html:6
msgid "Join Our Team"
msgstr "Takımımıza Katılın"

#: templates/emails/team_invitation.html:15
msgid "You've been invited to join our team!"
msgstr "Takımımıza katılmaya davet edildiniz!"

#: templates/emails/team_invitation.html:18
#, python-format
msgid ""
"\n"
"        You have been invited to join the <strong>%(company_name)s</strong> "
"team at Canvider ATS as a <strong>%(role)s</strong>.\n"
"        "
msgstr ""
"\n"
"        <strong>%(company_name)s</strong> takımına Canvider ATS'de "
"<strong>%(role)s</strong> olarak katılmaya davet edildiniz.\n"
"        "

#: templates/emails/team_invitation.html:26
msgid "Invitation Details"
msgstr "Davet Detayları"

#: templates/emails/team_invitation.html:30 templates/profile.html:66
#: templates/profile.html:111
msgid "Company"
msgstr "Şirket"

#: templates/emails/team_invitation.html:34
#: templates/manage_permissions.html:113 templates/manage_permissions.html:252
#: templates/manage_permissions.html:381
msgid "Role"
msgstr "Rol"

# msgid "Company"
# msgstr "Şirket"
# msgid "Role"
# msgstr "Rol"
#: templates/emails/team_invitation.html:38
msgid "Invitation Expires"
msgstr "Davet Süresi Bitiyor"

#: templates/emails/team_invitation.html:46 templates/register.html:11
msgid "Accept Invitation"
msgstr "Daveti Kabul Et"

# msgid "Accept Invitation"
# msgstr "Daveti Kabul Et"
#: templates/emails/team_invitation.html:51
msgid "Can't click the button?"
msgstr "Butona tıklayamıyor musunuz?"

#: templates/emails/team_invitation.html:53
msgid "Copy and paste this link into your browser:"
msgstr "Bu bağlantıyı kopyalayıp tarayıcınıza yapıştırın:"

#: templates/emails/team_invitation.html:61
msgid "Important Information"
msgstr "Önemli Bilgiler"

#: templates/emails/team_invitation.html:63
msgid "This invitation is valid until"
msgstr "Bu davet şu tarihe kadar geçerlidir"

#: templates/emails/team_invitation.html:64
msgid "You will need to create a password when accepting the invitation"
msgstr "Daveti kabul ederken bir şifre oluşturmanız gerekecek"

#: templates/emails/team_invitation.html:65
msgid "If you have any questions, please contact the administrator"
msgstr "Herhangi bir sorunuz varsa, lütfen yönetici ile iletişime geçin"

#: templates/emails/team_invitation.html:71
msgid "We look forward to having you on our team!"
msgstr "Sizi takımımızda görmek için sabırsızlanıyoruz!"

#: templates/feed.html:20 templates/feed.html:46
msgid "Loading..."
msgstr "Yükleniyor..."

#: templates/feed.html:32
msgid "Calendar"
msgstr "Takvim"

#: templates/feed.html:34
msgid "Day"
msgstr "Gün"

#: templates/feed.html:35
msgid "Week"
msgstr "Hafta"

#: templates/feed.html:36
msgid "Month"
msgstr "Ay"

#: templates/feed.html:51 templates/jobs.html:92 templates/people.html:64
msgid "Today"
msgstr "Bugün"

#: templates/feed.html:56
msgid "Click on a day with colored dots to view events"
msgstr "Renkli noktalarla tıklayarak etkinlikleri görüntüleyin"

#: templates/feed.html:61 templates/feed.html:74
msgid "Mon"
msgstr "Pzt"

#: templates/feed.html:62 templates/feed.html:75
msgid "Tue"
msgstr "Sal"

#: templates/feed.html:63 templates/feed.html:76
msgid "Wed"
msgstr "Çar"

#: templates/feed.html:64 templates/feed.html:77
msgid "Thu"
msgstr "Per"

#: templates/feed.html:65 templates/feed.html:78
msgid "Fri"
msgstr "Cum"

#: templates/feed.html:66 templates/feed.html:79
msgid "Sat"
msgstr "Cmt"

#: templates/feed.html:67 templates/feed.html:80
msgid "Sun"
msgstr "Paz"

#: templates/feed.html:96
msgid "Activity Feed"
msgstr "Güncel Gelişmeler"

#: templates/feed.html:100
msgid "Clear"
msgstr "Temizle"

#: templates/feed.html:147 templates/feed.html:3030
msgid "No Recent Activity"
msgstr "Son Aktivite Yok"

#: templates/feed.html:149 templates/feed.html:3032
msgid ""
"Activity will appear here when candidates apply, change status, or when you "
"post new jobs."
msgstr ""
"Adaylar başvurduğunda, durumunu değiştirdiğinde veya yeni ilanlar "
"yayınladığınızda aktivite burada görüntülenecektir."

#: templates/feed.html:166
msgid "Hot"
msgstr "Popüler"

#: templates/feed.html:166 templates/navbar.html:36
msgid "Jobs"
msgstr "İlanlar"

#: templates/feed.html:169
msgid "View All Jobs"
msgstr "Tüm İlanları Görüntüle"

#: templates/feed.html:186 templates/feed.html:245 templates/jobs.html:129
#: templates/navbar.html:42
msgid "Applicants"
msgstr "Adaylar"

#: templates/feed.html:203
msgid "No Hot Jobs Yet"
msgstr "Henüz Popüler İlan Yok"

#: templates/feed.html:205
msgid ""
"Create your first job posting to start attracting candidates and see "
"trending positions here."
msgstr ""
"ilk ilanınızı oluşturarak adayları çekmeyi başlatın ve burada popüler "
"pozisyonları görün."

#: templates/feed.html:209 templates/navbar.html:57
msgid "Create Job"
msgstr "İlan Oluştur"

#: templates/feed.html:212 templates/jobs.html:175
msgid "Browse Templates"
msgstr "Şablonları Gözden geçir"

#: templates/feed.html:228
msgid "Monthly Applicant Overview"
msgstr "Aylık Aday Genel Bakış"

#: templates/feed.html:267
msgid "Events for Date"
msgstr "Bu Tarihteki Etkinlikler"

#: templates/feed.html:289
msgid "Add New Event"
msgstr "Yeni Etkinlik Ekle"

#: templates/feed.html:299
msgid "Create New Event"
msgstr "Yeni Etkinlik Oluştur"

#: templates/feed.html:306
msgid "Enter event title"
msgstr "Etkinlik başlığını girin"

#: templates/feed.html:393
msgid "Select the relevant position"
msgstr "İlgili pozisyonu seçin"

#: templates/feed.html:400
msgid "No vacancies available"
msgstr "Su anda pozisyon yok"

#: templates/feed.html:413
msgid "Pick a Vacancy to see candidates"
msgstr "Adayları görmek için bir pozisyon seçin"

#: templates/feed.html:436
msgid "Enter meeting link"
msgstr "Toplantı linkini girin"

#: templates/feed.html:2975
msgid ""
"Are you sure you want to clear all activity notifications? This action "
"cannot be undone."
msgstr ""
"Bütün aktivite bildirimlerini silmek istediğinizden emin misiniz? Bu işlem "
"geri alınamaz."

#: templates/feed.html:3046
msgid "Activity feed cleared successfully"
msgstr "Aktivite bildirimleri başarıyla temizlendi!"

#: templates/feed.html:3048
msgid "Failed to clear activity feed"
msgstr "Aktivite bildirimleri temizlenemedi"

#: templates/feed.html:3053
msgid "An error occurred while clearing activity feed"
msgstr "Aktivite bildirimleri temizlenirken bir hata oluştu"

#: templates/job_details.html:7 templates/job_details.html:20
#: templates/job_details.html:51 templates/job_preview_publish.html:28
msgid "Job Description"
msgstr "İş Açıklaması"

#: templates/job_details.html:11 templates/job_preview_publish.html:17
msgid "Job Summary"
msgstr "İş Özeti"

#: templates/job_details.html:14 templates/job_preview_publish.html:21
msgid "Loading job details..."
msgstr "İş detayları yükleniyor..."

#: templates/job_details.html:24
msgid "Create new description"
msgstr "Yeni açıklama oluştur"

#: templates/job_details.html:28
msgid "Use saved template"
msgstr "Kayıtlı şablon kullan"

#: templates/job_details.html:33
msgid "Choose a template:"
msgstr "Bir şablon seçin:"

#: templates/job_details.html:35
msgid "Select a template"
msgstr "Şablon seçin"

#: templates/job_details.html:42
msgid "AI Job Description Generator"
msgstr "AI İş Tanımı Oluşturucu"

#: templates/job_details.html:43
msgid ""
"Let AI create a professional job description based on your job details above."
msgstr ""
"AI'ın yukarıdaki iş detaylarınıza dayanarak profesyonel bir iş tanımı "
"oluşturmasına izin verin."

#: templates/job_details.html:46
msgid "Generate with AI"
msgstr "AI ile Oluştur"

#: templates/job_details.html:52
msgid ""
"Describe the position, responsibilities, qualifications, and any other "
"relevant details."
msgstr ""
"Pozisyonu, sorumlulukları, nitelikleri ve diğer ilgili detayları açıklayın."

#: templates/job_details.html:106 templates/job_preview_publish.html:158
msgid "Back"
msgstr "Geri"

#: templates/job_details.html:111
msgid "Update Template"
msgstr "Şablonu Güncelle"

#: templates/job_details.html:114 templates/job_details.html:124
msgid "Save as Template"
msgstr "Şablon Olarak Kaydet"

#: templates/job_details.html:117
msgid "Save & Continue"
msgstr "Kaydet ve Devam Et"

#: templates/job_details.html:129
msgid "Template Title"
msgstr "Şablon Başlığı"

#: templates/job_details.html:130
msgid "Enter a name for this template"
msgstr "Bu şablon için bir isim girin"

#: templates/job_details.html:1060
msgid ""
"No job information found. Please go back and fill out the job details form."
msgstr ""
"Hiç iş bilgisi bulunamadı. Lütfen geri gidin ve ilan detaylarını doldurun."

#: templates/job_details.html:1093 templates/job_preview_publish.html:614
msgid "Salary Details"
msgstr "Maaş Detayları (İsteğe Bağlı)"

#: templates/job_details.html:1098 templates/job_preview_publish.html:621
msgid "Benefits & Highlights"
msgstr "Seçilen Yan Haklar ve Öne Çıkanlar"

#: templates/job_details.html:1108 templates/job_preview_publish.html:637
msgid "Skills"
msgstr "Yetenek"

#: templates/job_preferences.html:11 templates/job_preferences.html:17
#: templates/settings.html:20
msgid "Preferences"
msgstr "Tercihler"

#: templates/job_preferences.html:12
msgid "Configure standard options to streamline your job creation process"
msgstr ""
"İş oluşturma sürecinizi kolaylaştırmak için standart seçenekleri yapılandırın"

#: templates/job_preferences.html:26 templates/job_preferences.html:51
msgid "Work Schedules"
msgstr "Çalışma Zamanları"

#: templates/job_preferences.html:30 templates/job_preferences.html:84
msgid "Office Schedules"
msgstr "Ofis Ziyaret Sıklığı"

#: templates/job_preferences.html:34
msgid "Locations"
msgstr "Konumlar"

#: templates/job_preferences.html:38
msgid "Departments"
msgstr "Departmanlar"

#: templates/job_preferences.html:42
msgid "Language"
msgstr "Dil"

#: templates/job_preferences.html:52
msgid "Define standard work schedule types for your organization"
msgstr "Organizasyonunuz için standart çalışma zamanı türlerini tanımlayın"

#: templates/job_preferences.html:56
msgid "Add Work Schedule"
msgstr "Çalışma Zamanı Ekle"

#: templates/job_preferences.html:64
msgid "Search work schedules..."
msgstr "Çalışma zamanlarını ara..."

#: templates/job_preferences.html:67
msgid "Select All"
msgstr "Tümünü Seç"

#: templates/job_preferences.html:85
msgid "Define where and how employees work"
msgstr "Çalışanların nerede ve nasıl çalıştığını tanımlayın"

#: templates/job_preferences.html:89
msgid "Add Office Schedule"
msgstr "Ofis Programı Ekle"

#: templates/job_preferences.html:183
msgid "Language Settings"
msgstr "Dil Ayarları"

#: templates/job_preferences.html:184
msgid "Choose your preferred language for the application interface"
msgstr "Uygulama arayüzü için tercih ettiğiniz dili seçin"

#: templates/job_preferences.html:189
msgid "Interface Language"
msgstr "Arayüz Dili"

#: templates/job_preferences.html:190
msgid "Select the language you want to use for the application interface"
msgstr "Uygulama arayüzü için kullanmak istediğiniz dili seçin"

#: templates/job_preferences.html:210
msgid "Current"
msgstr "Anlık"

#: templates/job_preferences.html:223 templates/published_job_details.html:652
#: templates/workloupe_platform.html:213
msgid "Note:"
msgstr "Not:"

#: templates/job_preferences.html:223
msgid ""
"Changing the language will refresh the page to apply the new language "
"settings."
msgstr ""
"Dili değiştirmek, yeni dil ayarlarını uygulamak için sayfayı yenileyecektir."

#: templates/job_preview_publish.html:7 templates/job_preview_publish.html:162
msgid "Publish Job"
msgstr "İlanı Yayınla"

#: templates/job_preview_publish.html:11
msgid "Final Review"
msgstr "Son İnceleme"

#: templates/job_preview_publish.html:12
msgid "Please review the job details before publishing."
msgstr "Lütfen yayınlamadan önce ilan detaylarını inceleyin."

#: templates/job_preview_publish.html:32
msgid "Loading job description..."
msgstr "İlan detayları yükleniyor..."

#: templates/job_preview_publish.html:39
msgid "Publish To"
msgstr "Yayınlanacak Platformlar"

#: templates/job_preview_publish.html:41
msgid ""
"Select the job portals where you want to publish this job posting. <br> <br> "
"<i> if the portal you want to publish to is grayed out, it means that you "
"have not yet adjusted the related configuration settings. </i>"
msgstr ""
"Aşağıdakı portallardan birine yayınlamak istediğinizi seçin. <br> <br> <i> "
"Eğer bir portal gri renkli ise, bu portal için gerekli ayarları henüz "
"yapmadığınız anlamına gelir. </i>"

#: templates/job_preview_publish.html:61
msgid ""
"Professional networking platform with over 750 million users worldwide. "
"Selecting this option will open a new tab for you to complete the job "
"posting."
msgstr ""
"750 milyon kullanıcıya sahip profesyonel ağ platformu. Bu seçeneği seçmek "
"yeni bir tarayıcı sekmesi açacak ve iş ilanınızı paylaşmanıza destek "
"verecektir."

#: templates/job_preview_publish.html:82
msgid ""
"Job and company review site focusing on workplace transparency. Selecting "
"this option will open a new tab for you to complete the job posting."
msgstr ""
"İş ve şirket inceleme sitesi, işyerinde saydamlığı odaklanmaktadır. Bu "
"seçeneği seçmek yeni bir tarayıcı sekmesi açacak ve iş ilanınızı "
"paylaşmanıza destek verecektir."

#: templates/job_preview_publish.html:103
#, python-format
msgid ""
"Specialized job platform for tech and creative professionals powered by "
"Workloupe. Workloupe is 100%% free to use."
msgstr ""
"Teknoloji profesyonelleri için özel bir iş platformu. Workloupe tamamen "
"ücretsizdir."

#: templates/job_preview_publish.html:125
msgid ""
"One of the biggest remote job focused job platforms in the world. Posting to "
"Himalayas is free. "
msgstr ""
"Dünyadaki en büyük uzaktan iş odaklı iş platformlarından biridir. "
"Himalayalara gönderme ücretsizdir."

#: templates/job_preview_publish.html:147
msgid ""
"PostJobFree has more than 7 million jobs, and it's free to post to. Their "
"job portal is focused on simplicity and ease of use."
msgstr ""
"PostJobFree'da 7 milyondan fazla iş bulunur ve yayınlar ücretsizdir. Bu iş "
"portalı basitlik ve kullanım kolaylığına odaklanmaktadır."

#: templates/job_preview_publish.html:580
msgid ""
"No job information found. Please go back and fill out the job details form. "
msgstr ""
"Hiç iş bilgisi bulunamadı. Lütfen geri gidin ve ilan detaylarını doldurun."

#: templates/job_preview_publish.html:662
msgid "No job description found. Please go back and create a job description."
msgstr ""
"Hiç iş açıklaması bulunamadı. Lütfen geri gidin ve bir iş açıklaması "
"oluşturun."

#: templates/jobs.html:8
msgid "Job Listings"
msgstr "İş İlanları"

#: templates/jobs.html:18
msgid "Active Jobs"
msgstr "Aktif İlanlar"

#: templates/jobs.html:28
msgid "Total Applicants"
msgstr "Toplam Aday"

#: templates/jobs.html:38
msgid "Archived Jobs"
msgstr "Arşivlenmiş İlanlar"

#: templates/jobs.html:48
msgid "On-Hold Jobs"
msgstr "Beklemede olan İlanlar"

#: templates/jobs.html:61
msgid "All Departments"
msgstr "Tüm Departmanlar"

#: templates/jobs.html:71 templates/manage_permissions.html:212
#: templates/people.html:43
msgid "All Statuses"
msgstr "Tüm Durumlar"

#: templates/jobs.html:79 templates/people.html:51 templates/people.html:90
msgid "Location"
msgstr "Konum"

#: templates/jobs.html:81 templates/people.html:53
msgid "All Locations"
msgstr "Tüm Konumlar"

#: templates/jobs.html:89
msgid "Posted Date"
msgstr "Yayın Tarihi"

#: templates/jobs.html:91
msgid "All Time"
msgstr "Tüm Zamanlar"

#: templates/jobs.html:93 templates/people.html:65
msgid "This Week"
msgstr "Bu Hafta"

#: templates/jobs.html:94 templates/people.html:66
msgid "This Month"
msgstr "Bu Ay"

#: templates/jobs.html:95
msgid "Last Month"
msgstr "Geçen Ay"

#: templates/jobs.html:106 templates/people.html:76
msgid "Clear all filters"
msgstr "Tüm filtreleri temizle"

#: templates/jobs.html:135
msgid "Interviews"
msgstr "Görüşmeler"

#: templates/jobs.html:142
msgid "Hired"
msgstr "İşe alındı"

#: templates/jobs.html:146
msgid "Days Open"
msgstr "Açık Gün Sayısı"

#: templates/jobs.html:152
msgid "Closed on:"
msgstr "Kapatıldı:"

#: templates/jobs.html:152
msgid "Posted on:"
msgstr "Yayın Tarihi:"

#: templates/jobs.html:153
msgid "View Details"
msgstr "Detayları Görüntüle"

#: templates/jobs.html:166
msgid "No Job Postings Yet"
msgstr "Henüz İlan Yayınlanmadı"

#: templates/jobs.html:168
msgid ""
"You haven't published any job postings yet. Create your first job posting to "
"start attracting candidates."
msgstr ""
"Henüz bir ilan yayınlamadınız. İlk ilanınızı oluşturarak adayları çekmeyi "
"başlatın."

#: templates/jobs.html:172
msgid "Create Your First Job"
msgstr "İlk İlanınızı Oluşturun"

#: templates/jobs.html:186 templates/people.html:174
msgid "Showing"
msgstr "Gösteriliyor"

#: templates/jobs.html:186 templates/people.html:174
msgid "of"
msgstr "/"

#: templates/manage_permissions.html:11
msgid "Team & Invitations"
msgstr "Üyeler ve Davetler"

#: templates/manage_permissions.html:12
msgid "Manage your recruitment team and invite new members"
msgstr "İşe alım ekibinizi yönetin ve yeni üyeler davet edin"

#: templates/manage_permissions.html:17 templates/manage_permissions.html:39
#: templates/manage_permissions.html:73 templates/settings.html:62
msgid "Invitations"
msgstr "Davetler"

#: templates/manage_permissions.html:29 templates/manage_permissions.html:69
msgid "Team Members"
msgstr "Takım Üyeleri"

#: templates/manage_permissions.html:59
msgid "Administrators"
msgstr "Adminler"

#: templates/manage_permissions.html:84
msgid "Search team members..."
msgstr "Takım üyelerini ara..."

#: templates/manage_permissions.html:88 templates/manage_permissions.html:218
msgid "Role:"
msgstr "Rol:"

#: templates/manage_permissions.html:90 templates/manage_permissions.html:220
msgid "All Roles"
msgstr "Tüm Roller"

#: templates/manage_permissions.html:105 templates/manage_permissions.html:244
#: templates/people.html:87 templates/profile.html:49
#: templates/published_job_details.html:276
msgid "Name"
msgstr "İsim"

#: templates/manage_permissions.html:109 templates/manage_permissions.html:248
#: templates/profile.html:57 templates/profile.html:101
#: templates/register.html:24
msgid "Email"
msgstr "E-posta"

#: templates/manage_permissions.html:120 templates/manage_permissions.html:267
#: templates/profile.html:80 templates/published_job_details.html:285
msgid "Actions"
msgstr "İşlemler"

#: templates/manage_permissions.html:143
msgid "Deactivate"
msgstr "Devre Dışı Bırak"

#: templates/manage_permissions.html:147
msgid "Activate"
msgstr "Etkinleştir"

#: templates/manage_permissions.html:206
msgid "Search invitations..."
msgstr "Davetleri ara..."

#: templates/manage_permissions.html:210
msgid "Status:"
msgstr "Durum:"

#: templates/manage_permissions.html:231
msgid "Invite New Member"
msgstr "Yeni Üye Davet Et"

#: templates/manage_permissions.html:256
msgid "Sent Date"
msgstr "Gönderilen Tarih"

#: templates/manage_permissions.html:260
msgid "Expiry Date"
msgstr "Son Kullanım Tarihi"

#: templates/manage_permissions.html:305
msgid "No invitations found"
msgstr "Davet bulunamadı."

#: templates/manage_permissions.html:353
msgid "Invite New Team Member"
msgstr "Yeni Takım Üyesi Davet Et"

#: templates/manage_permissions.html:360
msgid "Recipient Information"
msgstr "Alıcı Bilgileri"

#: templates/manage_permissions.html:364 templates/profile.html:93
msgid "First Name"
msgstr "Ad"

#: templates/manage_permissions.html:365
msgid "Enter first name"
msgstr "Adını girin"

#: templates/manage_permissions.html:369 templates/profile.html:97
msgid "Last Name"
msgstr "Soyad"

#: templates/manage_permissions.html:370
msgid "Enter last name"
msgstr "Soyadını girin"

#: templates/manage_permissions.html:375 templates/signin.html:17
msgid "Email Address"
msgstr "E-posta Adresi"

#: templates/manage_permissions.html:376
msgid "Enter email address"
msgstr "E-posta adresini girin"

#: templates/manage_permissions.html:383 templates/manage_permissions.html:443
msgid "Select a role"
msgstr "Rol seçin"

#: templates/manage_permissions.html:384 templates/manage_permissions.html:444
msgid "Administrator"
msgstr "Yönetici"

#: templates/manage_permissions.html:385 templates/manage_permissions.html:437
#: templates/manage_permissions.html:445
msgid "Recruiter"
msgstr "İşe Alım Uzmanı"

#: templates/manage_permissions.html:386 templates/manage_permissions.html:446
msgid "Hiring Manager"
msgstr "İşe Alım Yöneticisi"

#: templates/manage_permissions.html:387 templates/manage_permissions.html:447
msgid "Interviewer"
msgstr "Görüşmenin Sorumlusu"

#: templates/manage_permissions.html:388 templates/manage_permissions.html:448
msgid "Read Only"
msgstr "Sadece Okuma"

#: templates/manage_permissions.html:395
msgid "Permissions"
msgstr "Kullanıcı izinlerini ayarla"

#: templates/manage_permissions.html:396
msgid ""
"Permissions are determined by the selected role. You can customize them "
"after the user has accepted the invitation."
msgstr ""
"İzinler, seçilen rol tarafından belirlenir. Kullanıcı davetiyeyi kabul "
"ettikten sonra bunları özelleştirebilirsiniz."

#: templates/manage_permissions.html:400
msgid "Role Descriptions:"
msgstr "Rol Açıklamaları:"

#: templates/manage_permissions.html:402
msgid "Administrator: Full access to all system features and settings."
msgstr "Tüm sistem özelliklerine ve ayarlara tam erişim."

#: templates/manage_permissions.html:403
msgid "Recruiter: Manage job postings, candidates, and interviews."
msgstr "İş ilanlarını, adayları ve görüşmeleri yönetin."

#: templates/manage_permissions.html:404
msgid "Hiring Manager: Review candidates and make hiring decisions."
msgstr "Adayları gözden geçirin ve işe alım kararlarını verin."

#: templates/manage_permissions.html:405
msgid "Interviewer: Conduct interviews and provide feedback."
msgstr "Görüşmeleri yapın ve geri bildirim sağlayın."

#: templates/manage_permissions.html:406
msgid "Read Only: View-only access to recruitment data."
msgstr "Sadece okuma erişimi."

#: templates/manage_permissions.html:414
msgid "Send Invitation"
msgstr "Davet Gönder"

#: templates/manage_permissions.html:425 templates/manage_permissions.html:464
msgid "Change Role"
msgstr "Rol Değiştir"

#: templates/manage_permissions.html:431
msgid "Team Member"
msgstr "Takım Üyesi"

#: templates/manage_permissions.html:436
msgid "Current Role"
msgstr "Mevcut Rol"

#: templates/manage_permissions.html:441
msgid "New Role*"
msgstr "Yeni Rol*"

#: templates/manage_permissions.html:453
msgid "Reason for Change (Optional)"
msgstr "Değişimin Nedeni (Opsiyonel)"

#: templates/manage_permissions.html:454
msgid "Provide a reason for this role change"
msgstr "Bu rol değişimine bir neden girin"

#: templates/manage_permissions.html:459
msgid ""
"Changing roles will update the user's permissions. They will be notified of "
"this change."
msgstr ""
"Rol değiştirme, kullanıcının izinlerini güncelleyecek. Bu değişiklik "
"hakkında bildirimde bulunacaklar."

#: templates/navbar.html:31
msgid "Feed"
msgstr "Ana Sayfa"

#: templates/navbar.html:94
msgid "Employee"
msgstr "Calışan"

#: templates/navbar.html:100
msgid "Profile"
msgstr "Profil"

#: templates/navbar.html:109
msgid "Logout"
msgstr "Çıkış Yap"

#: templates/navbar.html:118
msgid "Guest User"
msgstr "Zıyaretçi"

#: templates/navbar.html:121
msgid "Not logged in"
msgstr "Giriş Yapılmadı"

#: templates/navbar.html:126 templates/signin.html:97
msgid "Sign In"
msgstr "Giriş Yap"

#: templates/people.html:10
msgid "Applicant Tracking"
msgstr "Aday Takibi"

#: templates/people.html:14 templates/people.html:1034
msgid "Refresh Applicants"
msgstr "Adayları Yenile"

#: templates/people.html:18
msgid "Search applicants..."
msgstr "Aday ara..."

#: templates/people.html:33
msgid "All Positions"
msgstr "Tüm Pozisyonlar"

#: templates/people.html:63
msgid "All Dates"
msgstr "Tüm Tarihler"

#: templates/people.html:91
msgid "Experience (Years)"
msgstr "Deneyim (Yıl)"

#: templates/people.html:92 templates/published_job_details.html:282
msgid "Score"
msgstr "Puan"

#: templates/people.html:93
msgid "Applied On"
msgstr "Başvuru Tarihi"

#: templates/people.html:94
msgid "Action"
msgstr "İşlem"

#: templates/people.html:139 templates/published_job_details.html:343
msgid "View"
msgstr "İncele"

#: templates/people.html:154 templates/published_job_details.html:246
msgid "No Applicants Yet"
msgstr "Henüz Aday Yok"

#: templates/people.html:156
msgid ""
"Nobody has applied to your job postings yet. Once candidates start applying, "
"you'll see them here."
msgstr ""
"Henüz kimse ilanlarınıza başvurmadı. Adaylar başvurdukça burada göreceksiniz."

#: templates/people.html:160
msgid "View Job Postings"
msgstr "İlanları Görüntüle"

#: templates/people.html:163
msgid "Create New Job"
msgstr "Yeni İlan Oluştur"

#: templates/people.html:174
msgid "applicants"
msgstr "aday"

#: templates/people.html:227
msgid "Show"
msgstr "Göster"

#: templates/people.html:234
msgid "per page"
msgstr "sayfa başına"

#: templates/people.html:969
msgid "Processing..."
msgstr "İşleniyor..."

#: templates/people.html:1015
msgid "Success!"
msgstr "Başarılı!"

#: templates/people.html:1022 templates/workloupe_platform.html:1119
msgid "Error:"
msgstr "Hata:"

#: templates/people.html:1027
msgid "An error occurred while processing applications. Please try again."
msgstr "Başvuruları işlerken bir hata oluştu. Lütfen tekrar deneyin."

#: templates/profile.html:10
msgid "Your Profile"
msgstr "Profiliniz"

#: templates/profile.html:29
msgid "Change Photo"
msgstr "Fotoğraf Değiştir"

#: templates/profile.html:32
msgid "Profile Activity"
msgstr "Profil Aktivitesi"

#: templates/profile.html:33
msgid "Last Login:"
msgstr "Son Giriş:"

#: templates/profile.html:36
msgid "Account Created:"
msgstr "Hesap Oluşturulma:"

#: templates/profile.html:46 templates/profile.html:91
msgid "Personal Information"
msgstr "Kişisel Bilgiler"

#: templates/profile.html:63 templates/profile.html:109
#: templates/workloupe_platform.html:28
msgid "Company Information"
msgstr "Şirket Bilgileri"

#: templates/profile.html:83 templates/profile.html:163
#: templates/profile.html:185
msgid "Change Password"
msgstr "Şifre Değiştir"

#: templates/profile.html:105
msgid "Phone"
msgstr "Telefon"

#: templates/profile.html:124
msgid "Save Changes"
msgstr "Değişiklikleri Kaydet"

#: templates/profile.html:139
msgid "Upload Profile Photo"
msgstr "Profil Fotoğrafı Yükle"

#: templates/profile.html:146
msgid "Choose a photo (PNG or JPEG only)"
msgstr "Bir fotoğraf seçin (sadece PNG veya JPEG)"

#: templates/profile.html:153
msgid "Upload"
msgstr "Yükle"

#: templates/profile.html:170
msgid "Current Password"
msgstr "Mevcut Şifre"

#: templates/profile.html:174
msgid "New Password"
msgstr "Yeni Şifre"

#: templates/profile.html:178
msgid "Confirm New Password"
msgstr "Yeni Şifreyi Onayla"

#: templates/published_job_details.html:58
msgid "Notification"
msgstr "Bildirim"

#: templates/published_job_details.html:82
msgid "Total Applicants:"
msgstr "Toplam Aday:"

#: templates/published_job_details.html:87
msgid "Published At:"
msgstr "Yayın Tarihi:"

#: templates/published_job_details.html:111
msgid "Bulk Communication"
msgstr "Toplu İletişim"

#: templates/published_job_details.html:124
msgid "Expert Support Options"
msgstr "Uzman Destek Seçenekleri"

#: templates/published_job_details.html:136
msgid "Post on LinkedIn"
msgstr "LinkedIn'da Yayınla"

#: templates/published_job_details.html:148
#: templates/published_job_details.html:641
msgid "Change Vacancy Status"
msgstr "İlan Durumunu Değiştir"

#: templates/published_job_details.html:162
msgid "Applicants Over Time"
msgstr "Zaman İçinde Aday Sayısı"

#: templates/published_job_details.html:170
msgid "No Application Data Yet"
msgstr "Henüz Başvuru Verisi Yok"

#: templates/published_job_details.html:171
msgid ""
"Once candidates start applying, you'll see application trends over time here."
msgstr ""
"Adaylar başvurduklarında, burada zaman içindeki başvuru eğilimlerini "
"göreceksiniz."

#: templates/published_job_details.html:184
msgid "Number of Applicants by Job Portal"
msgstr "Adayların Başvuru Portalı Dağılımı"

#: templates/published_job_details.html:191
msgid "No Portal Data Yet"
msgstr "Henüz Portal Verisi Yok"

#: templates/published_job_details.html:192
msgid ""
"When applications come in, you'll see which job portals are most effective "
"here."
msgstr ""
"Adaylar başvurduklarında, en etkili iş portallerini burada göreceksiniz."

#: templates/published_job_details.html:202
msgid "Distribution of Applicants by Status"
msgstr "Adayların Durumuna Göre Dağılımı"

#: templates/published_job_details.html:209
msgid "No Status Data Yet"
msgstr "Henüz Durum Verisi Yok"

#: templates/published_job_details.html:210
msgid ""
"Application status distribution will appear here as you review candidates."
msgstr ""
"Başvuruları incelediğinizde, başvuru durumlarının dağılımı burada "
"görüntülenecek."

#: templates/published_job_details.html:225
msgid "Top Applicants"
msgstr "En İyi Adaylar"

#: templates/published_job_details.html:233
msgid "View All Applicants"
msgstr "Tüm Adayları Görüntüle"

#: templates/published_job_details.html:249
msgid ""
"Don't worry! Once candidates start applying to this position, they will "
"appear here. You can track their progress, review their profiles, and manage "
"the hiring process."
msgstr ""
"Endiselenmeyin! Adaylar bu pozisyona başvurduklarında, burada "
"görüntülenecek. İlerlemelerini takip edebilir, profillerini inceleyebilir ve "
"iş alma sürecini yönetebilirsiniz."

#: templates/published_job_details.html:256
msgid "What happens next?"
msgstr "Sonra ne olacak?"

#: templates/published_job_details.html:259
msgid "Candidates will apply through your job posting"
msgstr "Adaylar ilanınızı kullanarak başvuracaklar"

#: templates/published_job_details.html:260
msgid "You'll see their profiles and CVs here"
msgstr "Burada profillerini ve CV'lerini göreceksiniz"

#: templates/published_job_details.html:261
msgid "You can review, rate, and manage applications"
msgstr "Inceleme, değerlendirme ve yönetim yapabilirsiniz"

#: templates/published_job_details.html:262
msgid "Use the communication tools to contact candidates"
msgstr "Adaylara iletişim araçlarını kullanarak ulaşabilirsiniz"

#: templates/published_job_details.html:332
msgid "Not Rated"
msgstr "Puanlanmadı"

#: templates/published_job_details.html:360
msgid "Request Support From Experts"
msgstr "Uzman Destek İste"

#: templates/published_job_details.html:365
msgid ""
"We can provide vetted candidates from our talent pool, or help you during "
"the technical interviews to pick best fit for your expectations."
msgstr ""
"Talebiniz için teşekkür ederiz. Teknik görüşme sürecinde en iyi eşleşmeyi "
"seçmenize yardımcı olabiliriz."

#: templates/published_job_details.html:370
msgid "Enter Details"
msgstr "Detayları Girin"

#: templates/published_job_details.html:381
msgid "Request Candidates"
msgstr "Aday Talep Et"

#: templates/published_job_details.html:382
msgid "Request Interview Help"
msgstr "Görüşme Desteği Talep Et"

#: templates/published_job_details.html:396
msgid "Send Bulk Mail to Applicants"
msgstr "Toplu E-posta Gönder"

#: templates/published_job_details.html:401
msgid ""
"Use this form to send bulk emails to applicants based on their application "
"statuses."
msgstr ""
"Adayların durumuna göre toplu e-postalar göndermek için bu formu kullanın."

#: templates/published_job_details.html:406
msgid "Select Application Status"
msgstr "Başvuru Durumu Seçin"

#: templates/published_job_details.html:413
msgid "Select a status"
msgstr "Durum Seçin"

#: templates/published_job_details.html:420
msgid "Email Subject"
msgstr "E-posta Konusu"

#: templates/published_job_details.html:439
msgid "Enter internal notes for your team (optional)"
msgstr "Dahili notlarınızı girin (isteğe bağlı)"

#: templates/published_job_details.html:443
msgid "(sent to candidates)"
msgstr "(adaylara gönderilir)"

#: templates/published_job_details.html:450
msgid "Enter your email message"
msgstr "E-posta mesajınızı girin"

#: templates/published_job_details.html:462
msgid "Send notification emails to candidates"
msgstr "Adaylara bildirim e-postaları gönderin"

#: templates/published_job_details.html:466
msgid "Send Emails"
msgstr "E-postaları Gönder"

#: templates/published_job_details.html:482
msgid "Post this job on LinkedIn"
msgstr "LinkedIn'da Bu İlanı Yayınla"

#: templates/published_job_details.html:489
msgid ""
"Follow these simple steps to post your job on LinkedIn and reach more "
"candidates."
msgstr ""
"Bu kolay adımları takip ederek LinkedIn'da ilanınızı yayınlayabilir ve daha "
"fazla aday elde edebilirsiniz."

#: templates/published_job_details.html:496
msgid "Navigate to LinkedIn Job Posting"
msgstr "LinkedIn İş İlanı Yayın Sayfasına Git"

#: templates/published_job_details.html:499
msgid "Go to"
msgstr "Git"

#: templates/published_job_details.html:499
msgid "LinkedIn Job Posting Page"
msgstr "LinkedIn İş İlanı Yayın Sayfası"

#: templates/published_job_details.html:507
msgid "Copy Job Title"
msgstr "Ilan Başlığını Kopyala"

#: templates/published_job_details.html:509
msgid "Copy and paste the job title below:"
msgstr "Aşağıdaki ilan başlığını kopyala ve yapıştır:"

#: templates/published_job_details.html:526
msgid "Configure Job Settings"
msgstr "İş Ayarlarını Yapılandır"

#: templates/published_job_details.html:529
msgid "Select 'Use my own description' option and configure job details:"
msgstr ""
"'Kendi açıklamamı kullan' seçeneğini belirleyin ve iş detaylarını "
"yapılandırın:"

#: templates/published_job_details.html:531
msgid "Reference - Your job settings:"
msgstr "Referans - İş Ayarlarınız:"

#: templates/published_job_details.html:533
msgid "Location:"
msgstr "Konum:"

#: templates/published_job_details.html:534
msgid "Work Schedule:"
msgstr "Çalışma Programı:"

#: templates/published_job_details.html:535
msgid "Office Schedule:"
msgstr "Ofis Programı:"

#: templates/published_job_details.html:545
msgid "Copy Job Description"
msgstr "İş Açıklamasını Kopyala"

#: templates/published_job_details.html:548
msgid "Copy and paste the job description below:"
msgstr "Aşağıdaki iş açıklamasını kopyala ve yapıştır:"

#: templates/published_job_details.html:551
msgid ""
"Note: Delete any placeholder text in LinkedIn's editor before pasting. You "
"may have to re-apply some of the styling."
msgstr ""
"Not: LinkedIn düzenleyicisindeki yer tutucu metni silmeden önce yapıştırın. "
"Bazı stilleri yeniden uygulamak zorunda kalabilirsiniz."

#: templates/published_job_details.html:570
msgid "Configure Application Management"
msgstr "Başvuru Yönetimi Yapılandır"

#: templates/published_job_details.html:574
msgid "Click 'Continue' button"
msgstr "'Devam Et' tuşuna tıklayın"

#: templates/published_job_details.html:575
msgid "Find 'Manage applicants' option and click the pencil icon to edit"
msgstr ""
"'Adayları Yönet' seçeneğini bulun ve düzenleme için kalem simgesine tıklayın"

#: templates/published_job_details.html:576
msgid ""
"Change 'Manage Applications' from 'On LinkedIn' to 'On an External Website'"
msgstr ""
"'Adayları Yönet' seçeneğini 'LinkedIn'de' den 'Dış Web Sitesinde' olarak "
"değiştirin"

#: templates/published_job_details.html:577
msgid "Copy and paste the application URL below:"
msgstr "Aşağıdaki başvuru URL'sini kopyala ve yapıştır:"

#: templates/published_job_details.html:596
msgid "Review Qualifications"
msgstr "Kualifikasyonları İncele"

#: templates/published_job_details.html:599
msgid "Review and customize the ideal qualifications section:"
msgstr "ideal kualifikasyon bölümüne göz atın ve onaylayın:"

#: templates/published_job_details.html:601
msgid "Reference - Skills from your job:"
msgstr "Referans - İşinizden yetenekler:"

#: templates/published_job_details.html:615
msgid "Finalize and Publish"
msgstr "Sonlandır ve Yayınla"

#: templates/published_job_details.html:619
msgid "Confirm your identity using your work email if required"
msgstr "Gerekirse iş e-postanızı kullanarak kimliğinizi doğrulayın"

#: templates/published_job_details.html:620
msgid "Choose between free or promoted posting (Recommended: Free)"
msgstr ""
"Ücretsiz veya destekli gönderme arasında seçim yapın (Önerilen: Ücretsiz)"

#: templates/published_job_details.html:621
msgid "Click 'Post Job' button"
msgstr "'İşi Yayınla' tuşuna tıklayın"

#: templates/published_job_details.html:622
msgid "Your job is now live on LinkedIn!"
msgstr "Ilanınız artik LinkedIn'de yayında!"

#: templates/published_job_details.html:629
msgid ""
"Need help? Contact our support team if you encounter any issues during the "
"posting process."
msgstr ""
"Yardıma mi ihtiyacınız var? İlan sürecinde herhangi bir sorunla "
"karşılaşırsanız destek ekibimizle iletişime geçin."

#: templates/published_job_details.html:646
msgid "Current Status:"
msgstr "Mevcut Durum:"

#: templates/published_job_details.html:652
msgid "Changing the status will affect the visibility of the vacancy."
msgstr "İlan durumundaki değişiklikler ilanın görünürlüğünü etkileyecektir."

#: templates/published_job_details.html:655
msgid ""
"The vacancy will no longer exist on boards and be closed but be accesible "
"internally."
msgstr ""
"İlan panolardan kaldırılacak ve kapatılacak ancak dahili olarak erişilebilir "
"olacak."

#: templates/published_job_details.html:656
msgid "The vacancy will stop accepting new applications until changed."
msgstr "İlan yeniden açılana kadar aday kabul etmeyecek."

#: templates/published_job_details.html:657
msgid "The vacancy will be re-opened for new applications."
msgstr "Yeni adaylar için ilan yeniden açılacak."

#: templates/published_job_details.html:658
msgid "The vacancy will be permanently deleted. This action cannot be undone."
msgstr "İlan kalıcı olarak silinecek. Bu işlem geri alınamaz."

#: templates/published_job_details.html:664
msgid "Select New Status"
msgstr "Yeni Durum Seçin"

#: templates/published_job_details.html:687
msgid "Confirm Status"
msgstr "Durumu Onayla"

#: templates/register.html:15
msgid "This invitation has expired or already been used."
msgstr "Bu davet süresi dolmuş veya zaten kullanılmış."

#: templates/register.html:18
msgid "Hello"
msgstr "Merhaba"

#: templates/register.html:18
msgid "you've been invited to join"
msgstr "katılmaya davet edildiniz"

#: templates/register.html:18
msgid "as a"
msgstr "olarak"

#: templates/register.html:29
msgid "Create Password"
msgstr "Şifre Oluştur"

#: templates/register.html:34
msgid "Confirm Password"
msgstr "Şifreyi Onayla"

#: templates/register.html:39
msgid "Complete Registration"
msgstr "Kaydı Tamamla"

#: templates/register.html:58
msgid "Passwords do not match"
msgstr "Şifreler eşleşmiyor"

#: templates/registration_complete.html:14
msgid "Registration Complete!"
msgstr "Kayıt Tamamlandı!"

#: templates/registration_complete.html:15
msgid ""
"Your account has been created successfully. You can now log in to access the "
"system."
msgstr ""
"Hesabınız başarıyla oluşturuldu. Artık sisteme erişmek için giriş "
"yapabilirsiniz."

#: templates/registration_complete.html:17
msgid "Go to Login"
msgstr "Giriş Sayfasına Git"

#: templates/settings.html:10
msgid "Configure your recruitment workflow and manage your ATS settings"
msgstr "İşe alım iş akışınızı yapılandırın ve ATS ayarlarınızı yönetin"

#: templates/settings.html:21
msgid ""
"Configure default options for job creation including work schedules, office "
"locations, and role titles."
msgstr ""
"Çalışma zamanları, ofis konumları ve rol başlıkları dahil olmak üzere iş "
"oluşturma için varsayılan seçenekleri yapılandırın."

#: templates/settings.html:23
msgid "Define company work schedules"
msgstr "Şirket çalışma zamanlarını tanımlayın"

#: templates/settings.html:24
msgid "Set up office locations"
msgstr "Ofis konumlarını ayarlayın"

#: templates/settings.html:25
msgid "Standardize role titles"
msgstr "Rol başlıklarını standartlaştırın"

#: templates/settings.html:26
msgid "Configure office schedule options"
msgstr "Ofis programı seçeneklerini yapılandırın"

#: templates/settings.html:29
msgid "Manage Preferences"
msgstr "Tercihleri Yönet"

#: templates/settings.html:42
msgid ""
"Create, edit, and manage job description templates to streamline your job "
"posting process."
msgstr ""
"İş ilan sürecinizi kolaylaştırmak için iş tanımı şablonları oluşturun, "
"düzenleyin ve yönetin."

#: templates/settings.html:44
msgid "Build reusable job templates"
msgstr "Yeniden kullanılabilir iş şablonları oluşturun"

#: templates/settings.html:45
msgid "Save time on repetitive descriptions"
msgstr "Tekrarlayan açıklamalarda zaman kazanın"

#: templates/settings.html:46
msgid "Maintain consistent job postings"
msgstr "Tutarlı iş ilanları sürdürün"

#: templates/settings.html:47
msgid "Organize templates by department"
msgstr "Şablonları departmana göre düzenleyin"

#: templates/settings.html:50
msgid "Manage Templates"
msgstr "Şablonları Yönet"

#: templates/settings.html:63
msgid ""
"Invite team members to collaborate on your recruitment process and manage "
"user access."
msgstr ""
"Ekip üyelerini işe alım sürecinizde işbirliği yapmaya davet edin ve "
"kullanıcı erişimini yönetin."

#: templates/settings.html:65
msgid "Add colleagues to your ATS"
msgstr "ATS'nize meslektaşlar ekleyin"

#: templates/settings.html:66
msgid "Set user permissions"
msgstr "Kullanıcı izinlerini ayarlayın"

#: templates/settings.html:67
msgid "Track invitation status"
msgstr "Davet durumunu takip edin"

#: templates/settings.html:68
msgid "Manage team collaboration"
msgstr "Ekip işbirliğini yönetin"

#: templates/settings.html:71
msgid "Manage Invitations"
msgstr "Davetleri Yönet"

#: templates/settings.html:84
msgid "Job Portals"
msgstr "İş Portalları"

#: templates/settings.html:85
msgid ""
"Configure connections to external job boards and manage API credentials for "
"job publishing."
msgstr ""
"Harici iş panolarına bağlantıları yapılandırın ve iş yayınlama için API "
"kimlik bilgilerini yönetin."

#: templates/settings.html:87
msgid "Connect to major job boards"
msgstr "Büyük iş panolarına bağlanın"

#: templates/settings.html:88
msgid "Manage API tokens securely"
msgstr "API tokenlarını güvenli bir şekilde yönetin"

#: templates/settings.html:89
msgid "Customize portal preferences"
msgstr "Portal tercihlerini özelleştirin"

#: templates/settings.html:90
msgid "Track portal integration status"
msgstr "Portal entegrasyon durumunu takip edin"

#: templates/settings.html:93
msgid "(Coming Soon!)"
msgstr "(Yakında!)"

#: templates/settings.html:106
msgid "Careers Page & Workloupe Configurations"
msgstr "Kariyer Sayfası & Workloupe Ayarları"

#: templates/settings.html:107
msgid ""
"Adjust the look and feel of your company careers page and workloupe profile "
"to match your branding."
msgstr ""
"Kariyer sayfanızın ve Workloupe profilinizin görünümünü ve hissini "
"şirketinizi yansıtan şekilde ayarlayın."

#: templates/settings.html:109
msgid "Upload company photos"
msgstr "Şirket Fotoğraflarını Yükleyin"

#: templates/settings.html:110
msgid "Pick your colours"
msgstr "Renklerini Seçin"

#: templates/settings.html:111
msgid "Choose the components"
msgstr "Bileşenleri Seçin"

#: templates/settings.html:112
msgid "Upload your company logo and banner"
msgstr "Sirket logosunu ve bannerını yükleyin"

#: templates/settings.html:115
msgid "Manage Carees Page & Workloupe Configurations"
msgstr "Kariyer Sayfası & Workloupe Ayarlarını Yönetin"

#: templates/settings.html:126
msgid "Need Help?"
msgstr "Yardıma İhtiyacınız Var mı?"

#: templates/settings.html:127
msgid ""
"Our support team is ready to assist you with any questions about configuring "
"your ATS."
msgstr ""
"Destek ekibimiz ATS'nizi yapılandırma konusundaki tüm sorularınızda size "
"yardımcı olmaya hazır."

#: templates/settings.html:128
msgid "Contact Support"
msgstr "Destek ile İletişime Geçin"

#: templates/signin.html:9
msgid "Welcome Back"
msgstr "Tekrar Hoş Geldiniz"

#: templates/signin.html:10
msgid "Sign in to your account"
msgstr "Hesabınıza giriş yapın"

#: templates/signin.html:24
msgid "Enter your email"
msgstr "E-posta adresinizi girin"

#: templates/signin.html:38
msgid "Password"
msgstr "Şifre"

#: templates/signin.html:45
msgid "Enter your password"
msgstr "Şifrenizi girin"

#: templates/signin.html:72
msgid "Security Check"
msgstr "Güvenlik Kontrolü"

#: templates/signin.html:72
msgid "What is"
msgstr "Cevap nedir?"

#: templates/signin.html:79
msgid "Enter the answer"
msgstr "Cevabı girin"

#: templates/signin.html:89
msgid "Remember me"
msgstr "Beni Hatırla"

#: templates/signin.html:92
msgid "Forgot password?"
msgstr "Şifrenizi mi unuttunuz?"

#: templates/signin.html:106
msgid "Don't have an account?"
msgstr "Hesabınız yok mu?"

#: templates/signin.html:108
msgid "Contact us"
msgstr "Bize Ulaşın"

#: templates/wordpress_integration.html:14
msgid "WordPress Integration"
msgstr "WordPress Entegrasyonu"

#: templates/wordpress_integration.html:15
msgid "Configure your WordPress careers page"
msgstr "WordPress kariyer sayfanızı yapılandırın"

#: templates/wordpress_integration.html:23
msgid "WordPress Setup"
msgstr "WordPress Kurulumu"

#: templates/wordpress_integration.html:28
msgid "Choose your preferred WordPress integration method"
msgstr "Tercih ettiğiniz WordPress entegrasyon yöntemini seçin"

#: templates/wordpress_integration.html:32
msgid "Integration Method"
msgstr "Entegrasyon Yöntemi"

#: templates/wordpress_integration.html:34
msgid "Shortcode (Recommended)"
msgstr "Shortcode (Önerilen)"

#: templates/wordpress_integration.html:35
#: templates/wordpress_integration.html:187
msgid "WordPress Widget"
msgstr "WordPress Widgeti"

#: templates/wordpress_integration.html:36
#: templates/wordpress_integration.html:201
msgid "Custom Plugin"
msgstr "Ozelleştirilmiş Eklenti"

#: templates/wordpress_integration.html:63
msgid "Design Settings"
msgstr "Tasarım Ayarları"

#: templates/wordpress_integration.html:72
msgid "WordPress Theme Style"
msgstr "WordPress Tema Stili"

#: templates/wordpress_integration.html:74
msgid "Inherit from Theme"
msgstr "Temadan Miras Al"

#: templates/wordpress_integration.html:84
msgid "Responsive Design"
msgstr "Responsif Tasarım"

#: templates/wordpress_integration.html:97
msgid "Jobs Per Page"
msgstr "Sayfa Başına İlanlar"

#: templates/wordpress_integration.html:109
msgid "Show Job Filters"
msgstr "Ilan Filtrelerini Göster"

#: templates/wordpress_integration.html:116
msgid "Show Search Box"
msgstr "Arama Kutusunu Göster"

#: templates/wordpress_integration.html:123
msgid "Show Pagination"
msgstr "Sayfalama butonlarını Göster"

#: templates/wordpress_integration.html:132
msgid "Generate WordPress Code"
msgstr "WordPress Kodunu Oluştur"

#: templates/wordpress_integration.html:141
msgid "Preview & Instructions"
msgstr "Önizleme & Talimatlar"

#: templates/wordpress_integration.html:144
msgid "Preview"
msgstr "Önizleme"

#: templates/wordpress_integration.html:147
msgid "Instructions"
msgstr "Talimatlar"

#: templates/wordpress_integration.html:167
msgid "Shortcode Integration"
msgstr "Shortcode Entegrasyonu"

#: templates/wordpress_integration.html:169
msgid "Recommended Method"
msgstr "Tavsiye Edilen Yöntem"

#: templates/wordpress_integration.html:169
msgid "Easy to use and works with any WordPress theme"
msgstr "Kullanımı kolay ve herhangi bir WordPress temasıyla çalışır"

#: templates/wordpress_integration.html:172
msgid "Copy the shortcode below"
msgstr "Shortcode'u kopyala"

#: templates/wordpress_integration.html:173
msgid "Go to your WordPress admin panel"
msgstr "WordPress yönetim panelinize gidin"

#: templates/wordpress_integration.html:174
msgid "Edit the page where you want to display jobs"
msgstr "Ilanları görüntülemek istediğiniz sayfayı düzenleyin"

#: templates/wordpress_integration.html:175
msgid "Paste the shortcode in the content area"
msgstr "Shortcode'u içerik alanına yapıştırın"

#: templates/wordpress_integration.html:176
msgid "Save and publish the page"
msgstr "Kaydedin ve sayfayı yayınlayın"

#: templates/wordpress_integration.html:189
msgid "Perfect for sidebars and widget areas"
msgstr "Sidebar'lar ve widget alanları için mükemmeldir"

#: templates/wordpress_integration.html:192
msgid "Go to Appearance > Widgets in your WordPress admin"
msgstr "WordPress yönetim panelinizde Gorunum > Widgetlar'a gidin"

#: templates/wordpress_integration.html:193
msgid "Find the 'Workloupe Careers' widget"
msgstr "'Workloupe Kariyer' widgetını bulun"

#: templates/wordpress_integration.html:194
msgid "Drag it to your desired widget area"
msgstr "istediğiniz widget alanına sürükleyin"

#: templates/wordpress_integration.html:195
msgid "Configure the widget settings"
msgstr "Widget ayarlarını yapılandırın"

#: templates/wordpress_integration.html:196
msgid "Save the widget"
msgstr "Widget'ı Kaydet"

#: templates/wordpress_integration.html:203
msgid "Advanced option - requires technical knowledge"
msgstr "Ileri düzeyli seçenek - teknik bilgi gerektirir"

#: templates/wordpress_integration.html:206
msgid "Download the custom plugin file"
msgstr "Eklenti dosyasını indirin"

#: templates/wordpress_integration.html:207
msgid "Upload it to your WordPress plugins directory"
msgstr "WordPress eklentileri dizinine yükleyin"

#: templates/wordpress_integration.html:208
msgid "Activate the plugin in WordPress admin"
msgstr "WordPress yönetim panelinde eklentiyi etkinleştirin"

#: templates/wordpress_integration.html:209
msgid "Configure the plugin settings"
msgstr "Eklenti ayarlarını yapılandırın"

#: templates/wordpress_integration.html:210
msgid "Use shortcodes or widgets as needed"
msgstr "Shortcode'ları veya widget'ları gerektiğinde kullanın"

#: templates/wordpress_integration.html:214
msgid "Download Plugin"
msgstr "Eklentiyi İndir"

#: templates/wordpress_integration.html:231
msgid "WordPress Integration Code"
msgstr "WordPress Entegrasyonu Kodu"

#: templates/wordpress_integration.html:238
msgid "Copy the code below and follow the integration instructions."
msgstr "Asağıdaki kodu kopyalayın ve entegrasyon talimatlarını takip edin."

#: templates/wordpress_integration.html:243
msgid "WordPress Code"
msgstr "WordPress Kodu"

#: templates/wordpress_integration.html:256
msgid "Download Files"
msgstr "Dosyaları İndir"

#: templates/workloupe_platform.html:13
msgid "Workloupe Platform Setup"
msgstr "Workloupe Platform Kurulumu"

#: templates/workloupe_platform.html:14
msgid "Create and manage your company profile on workloupe.com"
msgstr "Workloupe.com'da şirket profilinizi oluşturun ve yönetin"

#: templates/workloupe_platform.html:18
msgid ""
"Update your existing profile information below. All changes will be saved "
"automatically."
msgstr ""
"Profil bilgilerinizi aşağıda güncelleyin. Tüm değişiklikler otomatik olarak "
"kaydedilecektir."

#: templates/workloupe_platform.html:36
msgid "Company name is required"
msgstr "Sirket adı gerekli"

#: templates/workloupe_platform.html:41
msgid "Company Email"
msgstr "Şirket E-postası"

#: templates/workloupe_platform.html:43
msgid "Please enter a valid email address"
msgstr "Lütfen geçerli bir e-posta adresi girin"

#: templates/workloupe_platform.html:51
msgid "Phone Number"
msgstr "Telefon Numarası"

#: templates/workloupe_platform.html:53
msgid "Include country code (e.g., ******-123-4567)"
msgstr "Ulke kodunu ekleyin (örn., +90 ************)"

#: templates/workloupe_platform.html:58
msgid "Website"
msgstr "Web Sitesi"

#: templates/workloupe_platform.html:60
msgid "Please enter a valid website URL"
msgstr "Lütfen geçerli bir web sitesi URL'si girin"

#: templates/workloupe_platform.html:66
msgid "Company Address"
msgstr "Şirket Adresi"

#: templates/workloupe_platform.html:67
msgid "Full company address including city, state, country"
msgstr "Tam şirket adresi (şehir, vilayet, ülke)"

#: templates/workloupe_platform.html:71
msgid "Office Locations"
msgstr "Ofis Konumları"

#: templates/workloupe_platform.html:72
msgid "Separate multiple locations with | (e.g., New York | London | Remote)"
msgstr "Ofis konumlarını | ile ayırın (örn., New York | Ankara | Remote)"

#: templates/workloupe_platform.html:73
msgid "Use | to separate multiple office locations"
msgstr "Ofis konumlarını ayırmak için | kullanın"

#: templates/workloupe_platform.html:77
msgid "Company Description"
msgstr "Şirket Açıklaması"

#: templates/workloupe_platform.html:78
msgid ""
"Describe your company, mission, values, and what makes it special. This will "
"be prominently displayed on your profile."
msgstr ""
"Sirketinizi, misyonunuzu, degerlerinizi ve neyin onu özel kılanını "
"açıklayın. Bu profilinizde öne çıkarılacaktır."

#: templates/workloupe_platform.html:79
msgid "Company description is required"
msgstr "Sirket açıklaması gerekli"

#: templates/workloupe_platform.html:80
msgid "Minimum 50 characters recommended for better visibility"
msgstr "En az 50 karakter girin (daha iyi görünürlük için)"

#: templates/workloupe_platform.html:88
msgid "Company Details"
msgstr "Şirket Detayları"

#: templates/workloupe_platform.html:94
msgid "Industry"
msgstr "Endüstri"

#: templates/workloupe_platform.html:96
msgid "Select Industry"
msgstr "Endüstriyi Seçin"

#: templates/workloupe_platform.html:97
msgid "Technology"
msgstr "Teknoloji"

#: templates/workloupe_platform.html:98
msgid "Healthcare"
msgstr "Sağlık"

#: templates/workloupe_platform.html:99
msgid "Finance"
msgstr "Finans"

#: templates/workloupe_platform.html:100
msgid "Education"
msgstr "Eğitim"

#: templates/workloupe_platform.html:101
msgid "Manufacturing"
msgstr "Uretim"

#: templates/workloupe_platform.html:102
msgid "Retail"
msgstr "Perakende"

#: templates/workloupe_platform.html:103
msgid "Consulting"
msgstr "Danışmanlık"

#: templates/workloupe_platform.html:104
msgid "Marketing & Advertising"
msgstr "Reklam & Pazarlama"

#: templates/workloupe_platform.html:105
msgid "Real Estate"
msgstr "Mülk / Emlak"

#: templates/workloupe_platform.html:106
msgid "Non-profit"
msgstr "Hayır Kuruluşu"

#: templates/workloupe_platform.html:107
msgid "Other"
msgstr "Diğer"

#: templates/workloupe_platform.html:113
msgid "Company Size"
msgstr "Şirket Boyutu"

#: templates/workloupe_platform.html:114
msgid "Number of employees"
msgstr "Calisan Sayisi"

#: templates/workloupe_platform.html:115
msgid "Please enter a valid number of employees"
msgstr "Lütfen geçerli bir calisan sayisi girin"

#: templates/workloupe_platform.html:116
msgid "Enter the total number of employees in your company"
msgstr "Total calisan sayisini girin"

#: templates/workloupe_platform.html:122
msgid "Social Media Links"
msgstr "Sosyal Medya Bağlantıları"

#: templates/workloupe_platform.html:122
msgid "Optional"
msgstr "Opsiyonel"

#: templates/workloupe_platform.html:128
msgid "Please enter a valid LinkedIn URL"
msgstr "Lütfen geçerli bir LinkedIn URL'si girin"

#: templates/workloupe_platform.html:133
msgid "Please enter a valid Instagram URL"
msgstr "Lütfen geçerli bir Instagram URL'si girin"

#: templates/workloupe_platform.html:140
msgid "Please enter a valid Twitter URL"
msgstr "Lütfen geçerli bir Twitter URL'si girin"

#: templates/workloupe_platform.html:145
msgid "Please enter a valid Github URL"
msgstr "Lütfen geçerli bir Github URL'si girin"

#: templates/workloupe_platform.html:152
msgid "Please enter a valid Facebook URL"
msgstr "Lütfen geçerli bir Facebook URL'si girin"

#: templates/workloupe_platform.html:157
msgid "Please enter a valid Glassdoor URL"
msgstr "Lütfen geçerli bir Glassdoor URL'si girin"

#: templates/workloupe_platform.html:161
msgid ""
"Add your company's social media profiles to increase visibility. URLs will "
"be validated automatically."
msgstr ""
"Sirketinizin sosyal medya profillerini ekleyin. URL'ler otomatik olarak "
"doğrulanacaktır."

#: templates/workloupe_platform.html:169
msgid "Branding Assets"
msgstr "Marka Varlıkları"

#: templates/workloupe_platform.html:169
msgid "(Publicly Visible)"
msgstr "(Herkese açık)"

#: templates/workloupe_platform.html:177
msgid "Recommended: 300x300px, PNG or JPG. Max 3MB."
msgstr "Tavsi Edilen: 300x300px, PNG veya JPG. Maksimum 3MB."

#: templates/workloupe_platform.html:181
msgid "Current logo - upload new file to replace"
msgstr "Mevcut logo - yeni dosya yükleyin ve yerine koyun"

#: templates/workloupe_platform.html:185 templates/workloupe_platform.html:1165
msgid "No logo uploaded"
msgstr "Logo yüklenmedi"

#: templates/workloupe_platform.html:193
msgid "Company Banner"
msgstr "Şirket Bannerı"

#: templates/workloupe_platform.html:195
msgid "Recommended: 1200x400px, PNG or JPG. Max 3MB."
msgstr "Tavsiye Edilen: 1200x400px, PNG veya JPG. Maksimum 3MB."

#: templates/workloupe_platform.html:199
msgid "Current banner - upload new file to replace"
msgstr "Mevcut banner - yeni dosya yükleyin ve yerine koyun"

#: templates/workloupe_platform.html:203 templates/workloupe_platform.html:1172
msgid "No banner uploaded"
msgstr "Banner yüklenmedi"

#: templates/workloupe_platform.html:213
msgid ""
"If you don't have online versions of your logo or banner, you can upload "
"them here. They will be stored securely and made publicly accessible for "
"your profile."
msgstr ""
"Logonuz veya bannerınızın çevrimiçi sürümleriniz yoksa, buraya "
"yükleyebilirsiniz. Güvenli bir şekilde saklanacak ve profiliniz için halka "
"açık erişilebilir olacaktır."

#: templates/workloupe_platform.html:221
msgid "Company Gallery"
msgstr "Şirket Galerisi"

#: templates/workloupe_platform.html:226
msgid "Important:"
msgstr "Onemli:"

#: templates/workloupe_platform.html:226
msgid ""
"Photos will be public once you publish your profile. Maximum 50 photos "
"allowed. We reserve the right to remove inappropriate content. Only upload "
"professional, work-appropriate images."
msgstr ""
"Resimler profilinizi yayınladığınızda halka açık olacaktır. Maksimum 50 "
"resim izin verilir. Uygun olmayan içerikleri kaldırmak için iznimiz var. "
"Sadece profesyonel, işe uygun resimler yükleyin."

#: templates/workloupe_platform.html:232
msgid "Current Photos"
msgstr "Mevcut Resimler"

#: templates/workloupe_platform.html:238
msgid "Loading existing photos..."
msgstr "Mevcut resimler yükleniyor..."

#: templates/workloupe_platform.html:246
msgid "Upload New Photos"
msgstr "Yeni Resimler Yükle"

#: templates/workloupe_platform.html:249
msgid ""
"Select multiple photos (JPG, PNG, WebP). Max 5MB per photo. Showcase your "
"company culture, office, team, events, etc."
msgstr ""
"Çoklu resim seçin (JPG, PNG, WebP). Her resim için maksimum 5MB. Şirket "
"kültürünüzü, ofisini, ekibinizi, etkinliklerinizi vb. gösterin."

#: templates/workloupe_platform.html:260
msgid "Uploading photos..."
msgstr "Resimler yükleniyor..."

#: templates/workloupe_platform.html:268
msgid "Reset Form"
msgstr "Formu Sıfırla"

#: templates/workloupe_platform.html:272
msgid "Save & Publish Profile"
msgstr "Kaydet & Profili Yayımla"

#: templates/workloupe_platform.html:279
msgid ""
"Your data is secure and will only be used for your public company profile"
msgstr "Veriniz güvenli ve yalnızca profiliniz için kullanılacaktır."

#: templates/workloupe_platform.html:295
msgid "Profile Published Successfully!"
msgstr "Profil başarıyla yayınlandı!"

#: templates/workloupe_platform.html:302
msgid "Your company is now live on Workloupe!"
msgstr "Sirketiniz Workloupe'da yayında!"

#: templates/workloupe_platform.html:303
msgid ""
"Your company profile has been created and published on workloupe.com. "
"Candidates can now discover your company and apply to your jobs."
msgstr ""
"Sirket profiliniz workloupe.com'da oluşturuldu ve yayınlandı. Adaylar şimdi "
"sirketinizi keşfedip ve işe başvurabilir."

#: templates/workloupe_platform.html:307
msgid "Your Profile URL:"
msgstr "Profil URL'niz:"

#: templates/workloupe_platform.html:321
msgid "Share Your Profile"
msgstr "Profilinizi Paylaş"

#: templates/workloupe_platform.html:322
msgid "Share this link with candidates and on social media"
msgstr "Bu bağlantıyı adaylarla ve sosyal medya üzerinde paylaşın"

#: templates/workloupe_platform.html:330
msgid "Update Anytime"
msgstr "Her Zaman Güncelle"

#: templates/workloupe_platform.html:331
msgid "Return to this page to update your profile information"
msgstr "Bu sayfaya geri dönüp profil bilgilerinizi güncelleyin"

#: templates/workloupe_platform.html:341
msgid "View Live Profile"
msgstr "Canlı Profili Görüntüle"

#: templates/workloupe_platform.html:825
msgid "New logo selected - will be uploaded on save"
msgstr "Yeni logo seçildi - kaydetme üzerine yüklenecek"

#: templates/workloupe_platform.html:842
msgid "New banner selected - will be uploaded on save"
msgstr "Yeni banner seçildi - kaydetme üzerine yüklenecek"

#: templates/workloupe_platform.html:850
msgid "Please select a valid image file (JPG, PNG, WebP)"
msgstr "Lütfen geçerli bir resim dosyası seçin (JPG, PNG, WebP)"

#: templates/workloupe_platform.html:856
msgid "File size must be less than"
msgstr "Dosya boyut limiti "

#: templates/workloupe_platform.html:868
msgid "No existing photos found"
msgstr "Mevcut resim bulunamadı"

#: templates/workloupe_platform.html:889
msgid "Remove image"
msgstr "Resmi Kaldır"

#: templates/workloupe_platform.html:919
msgid "Failed to remove image. Please try again."
msgstr "Resim kaldırılamadı. Lütfen tekrar deneyin."

#: templates/workloupe_platform.html:924
msgid "An error occurred while removing the image."
msgstr "Resim kaldırılırken bir hata oluştu."

#: templates/workloupe_platform.html:953
msgid "You can only upload"
msgstr ""

#: templates/workloupe_platform.html:953
msgid "more photos. Maximum"
msgstr ""

#: templates/workloupe_platform.html:953
msgid "photos allowed."
msgstr ""

#: templates/workloupe_platform.html:1042
msgid "Please fix the validation errors before submitting."
msgstr "Lütfen göndermeden önce doğrulama hatalarını çözün."

#: templates/workloupe_platform.html:1097
msgid "Publishing..."
msgstr "Yayınlanıyor..."

#: templates/workloupe_platform.html:1119
msgid "Unknown error occurred"
msgstr "Bilinmeyen bir hata oluştu"

#: templates/workloupe_platform.html:1124
msgid "An error occurred while publishing your profile. Please try again."
msgstr "Profilinizi yayınlarken bir hata oluştu. Lütfen tekrar deneyin."

#: templates/workloupe_platform.html:1153
msgid "Failed to copy URL. Please copy it manually."
msgstr "URL kopyalanamadı. Lütfen elle kopyalayın."

#: templates/workloupe_platform.html:1158
msgid ""
"Are you sure you want to reset the form? All unsaved changes will be lost."
msgstr ""
"Formu sıfırlamak istediğinizden emin misiniz? Kaydedilmemiş tüm "
"değişiklikler kaybolacak."

#~ msgid "Online Interview"
#~ msgstr "Online görüşmesi"

#~ msgid "Technical Assessment"
#~ msgstr "Teknik değerlendirme"

#~ msgid "Final Interview"
#~ msgstr "Son görüşme"

#~ msgid "Face to Face Interview"
#~ msgstr "Yüz yüze görüşme"

#~ msgid "Office Visit"
#~ msgstr "Ofis ziyareti"

#~ msgid "Hot Jobs"
#~ msgstr "Popüler İlanlar"

#~ msgid "Signin"
#~ msgstr "Giriş Yap"

#~ msgid "View Application"
#~ msgstr "Başvuruyu Görüntüle"

#~ msgid "Download the CV"
#~ msgstr "CV'yi İndir"

#~ msgid "Remove Link"
#~ msgstr "Link Kaldır"

#~ msgid "Comments"
#~ msgstr "Yorumlar"

#~ msgid "Latest Update"
#~ msgstr "Son Güncelleme"

#~ msgid "Your browser does not support PDFs."
#~ msgstr "Tarayıcınız PDF'leri desteklemiyor."

#~ msgid "Font Size"
#~ msgstr "Yazı Boyutu"

#~ msgid "Very Small"
#~ msgstr "Çok Küçük"

#~ msgid "Small"
#~ msgstr "Küçük"

#~ msgid "Normal"
#~ msgstr "Normal"

#~ msgid "Medium"
#~ msgstr "Orta"

#~ msgid "Large"
#~ msgstr "Büyük"

#~ msgid "Very Large"
#~ msgstr "Çok Büyük"

#~ msgid "Extra Large"
#~ msgstr "Ekstra Büyük"

#~ msgid "Align Left"
#~ msgstr "Sola Hizala"

#~ msgid "Align Center"
#~ msgstr "Ortaya Hizala"

#~ msgid "Align Right"
#~ msgstr "Sağa Hizala"

#~ msgid "Insert Link"
#~ msgstr "Link Ekle"

#~ msgid "Add Emoji"
#~ msgstr "Emoji Ekle"

#~ msgid "No applicants found matching the current filters."
#~ msgstr "Mevcut filtrelere uygun aday bulunamadı."

#~ msgid "<EMAIL>"
#~ msgstr "<EMAIL>"

#, fuzzy
#~| msgid "Loading job details..."
#~ msgid "Loading job details...</div>"
#~ msgstr "İş detayları yükleniyor..."

#~ msgid "Decision"
#~ msgstr "Karar"

#~ msgid "Candidates Hired"
#~ msgstr "İşe Alınan Adaylar"

#~ msgid "A new vacancy"
#~ msgstr "Yeni iş ilanı"

#~ msgid "moved to"
#~ msgstr "taşıdı"

#~ msgid "for"
#~ msgstr "için"
